import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { CoachService } from "./coach.service";
import { Coach<PERSON>ontroller } from "./coach.controller";
import { MongooseModule } from "@nestjs/mongoose";
import { Coach, CoachSchema } from "./schemas/coach.schema";
import { UploadImageModule } from "src/upload-image/upload-image.module";
import { UsersModule } from "src/users/users.module";
import { Chat, ChatSchema } from "src/chats/schemas/chat.schema";

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Coach.name, schema: CoachSchema },
      { name: Chat.name, schema: ChatSchema },
    ]),
    UploadImageModule,
    UsersModule,
  ],

  controllers: [CoachController],
  providers: [CoachService],
  exports: [CoachService],
})
export class CoachModule {}
