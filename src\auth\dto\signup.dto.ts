// export class CreateUserDto {}
import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsString, IsEmail, IsOptional } from 'class-validator'

export class SignUpDto {
  @ApiProperty({ example: 'jon wick' })
  @IsString()
  name: string

  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail()
  email: string

  @ApiProperty({ example: 'causewaysecret' })
  @IsString()
  password: string
}
