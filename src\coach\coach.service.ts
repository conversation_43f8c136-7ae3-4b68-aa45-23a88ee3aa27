import {
  HttpException,
  HttpStatus,
  Injectable,
  UnauthorizedException,
} from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import * as bcrypt from "bcrypt";
import { Coach } from "./schemas/coach.schema";
import { JwtService } from "@nestjs/jwt";
import { MailerService } from "@nestjs-modules/mailer";
import { SignUpDto } from "./dto/signup.dto";
import { ForgotPasswordDto } from "./dto/forgot-password.dto";
import { ResetPasswordDto } from "./dto/reset-password.dto";
import {
  comparePassword,
  generateAndSaveCode,
  generateFileName,
  isExpire,
} from "src/utils/comman";
import { VerifyOtpDto } from "./dto/verify-otp.dto";
import { ICoach } from "./interfaces/coach.interface";
import { UploadImageService } from "src/upload-image/upload-image.service";
import { UsersService } from "src/users/users.service";
import { Chat } from "src/chats/schemas/chat.schema";
import { GetCoachesDto } from "./dto/get-coaches.dto";

@Injectable()
export class CoachService {
  constructor(
    @InjectModel(Coach.name) private coachModel: Model<Coach>,
    @InjectModel(Chat.name) private chatModel: Model<Chat>,

    private jwtService: JwtService,
    private readonly mailerService: MailerService,
    private readonly uploadImageService: UploadImageService,
    private readonly userService: UsersService
  ) { }

  async signIn(email: string, password: string) {
    let user = await this.coachModel.findOne({ email });
    if (!user || !(await comparePassword(password, user.password))) {
      throw new UnauthorizedException();
    }
    const payload = {
      name: user.name,
      email: user.email,
      id: user["_id"],
      userType: user.userType,
    };
    let url = "";
    if (user.photo) {
      url = await this.uploadImageService.getObjectSignedUrl(user.photo);
      user.photo = url;
    }

    const chats = await this.chatModel
      .find({ users: { $in: [user.id] } })
      .populate("lastMessage");

    user = user.toObject();
    user["chats"] = chats;

    return {
      access_token: await this.jwtService.signAsync(payload),
      ...user,
    };
  }

  async signUp(createAuthDto: SignUpDto) {
    const CHAR_SET =
      "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    let randomCode = "";
    let length = 8;
    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * CHAR_SET.length);
      randomCode += CHAR_SET.charAt(randomIndex);
    }
    const userExist = await this.coachModel.findOne({
      email: createAuthDto.email,
    });
    if (userExist) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: "Email Already exist",
        },
        HttpStatus.BAD_REQUEST
      );
    }
    const hashedPassword = await bcrypt.hash(createAuthDto.password, 10);
    const signupData = { ...createAuthDto, password: hashedPassword };
    const coach = await this.coachModel.create({
      ...signupData,
      referralCode: randomCode,
      userType: "coach",
    });

    // create entry in user table
    await this.userService.create({
      name: coach.name,
      email: coach.email,
      userType: coach.userType,
      userId: coach._id,
    });
    const payload = {
      name: coach.name, email: coach.email, id: coach.id,
      userType: "coach",
    };
    return {
      access_token: await this.jwtService.signAsync(payload),
      name: coach.name,
      email: coach.email,
      id: coach.id,
      referralCode: coach.referralCode,
      userType: coach.userType,
    };
  }

  async forgotPassword(forgotPasswordDto: ForgotPasswordDto) {
    const user = await this.coachModel.findOne({
      email: forgotPasswordDto.email,
    });
    if (!user) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: "Invalid Email",
        },
        HttpStatus.BAD_REQUEST
      );
    }

    let { randomCode, timestampAfterFiveMinutes } = generateAndSaveCode();

    const updatedUser = await this.coachModel.updateOne(
      { email: forgotPasswordDto.email },
      {
        code: randomCode,
        verificationCodeExpiresAt: timestampAfterFiveMinutes,
      }
    );
    if (!updatedUser) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: "User not updated",
        },
        HttpStatus.BAD_REQUEST
      );
    } else {
      await this.mailerService.sendMail({
        to: forgotPasswordDto.email,
        subject: "Reset Password OTP",
        text: `Your OTP for resetting password is ${randomCode}. This OTP will expire in 5 minutes.`,
      });
      return {
        success: true,
        message: `OTP sent to ${forgotPasswordDto.email}`,
      };
    }
  }

  async getProfile(email: string): Promise<any> {
    let user = await this.coachModel.findOne({ email: email });
    if (user.photo) {
      const url = await this.uploadImageService.getObjectSignedUrl(user.photo);
      user["photo"] = url;
    }

    const chats = await this.chatModel
      .find({ users: { $in: [user.id] } })
      .populate("lastMessage");

    user = user.toObject();
    user["chats"] = chats;

    return user;
  }

  async verifyOtp(verifyOtpDto: VerifyOtpDto): Promise<any> {
    const user = await this.coachModel.findOne({ email: verifyOtpDto.email });
    if (!user) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: "Email does not exist",
        },
        HttpStatus.BAD_REQUEST
      );
    }

    const { code, verificationCodeExpiresAt } = user;
    if (
      !code ||
      code !== verifyOtpDto.otp ||
      isExpire(verificationCodeExpiresAt)
    ) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: "Invalid OTP",
        },
        HttpStatus.BAD_REQUEST
      );
    }

    return { success: true, message: "otp verified successfully" };
  }

  async resetPassword(dto: ResetPasswordDto): Promise<any> {
    const user = await this.coachModel.findOne({
      email: dto.email,
    });

    if (!user) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: `no user found with email ${dto.email}`,
        },
        HttpStatus.BAD_REQUEST
      );
    }

    if (
      !user.code ||
      dto.otp !== user.code ||
      isExpire(user.verificationCodeExpiresAt)
    ) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: `Invalid OTP code`,
        },
        HttpStatus.BAD_REQUEST
      );
    }

    // Reset password
    const hashPassword = await bcrypt.hash(dto.password, 10);
    await this.coachModel.updateOne(
      { email: dto.email },
      {
        password: hashPassword,
      }
    );

    return { success: true, message: "password updated successfully" };
  }

  async update(keyValue: any, updateUserDto: any): Promise<ICoach> {
    let updatedUser = await this.coachModel.findOneAndUpdate(
      keyValue,
      { ...updateUserDto },
      {
        new: true,
      }
    );

    let url = "";
    if (updatedUser.photo) {
      url = await this.uploadImageService.getObjectSignedUrl(updatedUser.photo);
    }
    updatedUser["photo"] = url;
    return updatedUser;
  }

  async changePassword(payload: any, user: any) {
    const { oldPassword, newPassword } = payload;
    const verifyPass = await comparePassword(oldPassword, user.password);
    if (!verifyPass) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: `Old password is incorrect`,
        },
        HttpStatus.BAD_REQUEST
      );
    }
    const hashPassword = await bcrypt.hash(newPassword, 10);

    return await this.update({ email: user.email }, { password: hashPassword });
  }
  async uploadFile(file: any, user: any) {
    const imageName = generateFileName();
    const imageData = await this.uploadImageService.uploadFile(
      file,
      user,
      imageName
    );
    await this.update({ _id: user.id }, { photo: imageData.imageName });
    return imageData;
  }

  async findCoach(keyValue: any) {
    const coach = await this.coachModel.findOne(keyValue);
    return coach;
  }

  // FIND ALL COACHES
  async findAll(filters: GetCoachesDto) {
    const { pageNumber, pageSize, sort } = filters;
    const query = this.coachModel.find().select("-password");

    // TOTAL COUNT OF COACHES
    const count = await this.coachModel.countDocuments(query);

    // SORTING 
    const sortOrder = sort === "asc" ? 1 : -1;
    query.sort({ createdAt: sortOrder });

    // PAGINATION
    if (pageNumber && pageSize) {
      const skipDocs = (pageNumber - 1) * pageSize;
      query.skip(skipDocs).limit(pageSize);
    }
    const coaches = await query.exec();

    return {
      count,
      pageNumber,
      pageSize,
      coaches,
    };
  }
}
