import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpStatus,
  HttpCode,
  BadRequestException,
  HttpException,
  NotFoundException,
  Query,
  Req,
} from "@nestjs/common";
import { UsersService } from "./users.service";
import { UpdateUserDto } from "./dto/update-user.dto";
import { ApiBearerAuth, ApiTags } from "@nestjs/swagger";
import { Admin } from "../decorators/admin.decorator";
import { Public } from "src/auth/decorators/public.decorator";

@ApiBearerAuth()
@Admin()
@ApiTags("Users (Admin)")
@Controller("users")
export class UsersController {
  constructor(private readonly usersService: UsersService) {}
  @Get()
  async findAll() {
    const res = await this.usersService.findAll();

    const users = res.map((user) => {
      const { password, ...userWithoutPassword } = user;
      return userWithoutPassword;
    });
    return users;
  }

  @Public()
  @Get("/active-deactive")
  async getActiveDeactiveUsers(@Query() query?: any) {
    const { active } = query;
    const result = await this.usersService.getUsers(active);

    const users = result.map((user) => {
      const { password, ...userWithoutPassword } = user;
      return userWithoutPassword;
    });
    return users;
  }

  @Get(":id")
  @HttpCode(HttpStatus.OK)
  async findOne(@Param("id") id: string) {
    const res = await this.usersService.findOne({ _id: id });
    if (!res) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
    const { password, ...user } = res;
    console.log(password, user);

    return user;
  }

  @Patch(":id")
  async update(@Param("id") id: string, @Body() updateUserDto: UpdateUserDto) {
    const filter = { _id: id };
    const res = await this.usersService.update(filter, updateUserDto);
    if (!res) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
    const { password, ...userData } = res;

    return userData;
  }
}
