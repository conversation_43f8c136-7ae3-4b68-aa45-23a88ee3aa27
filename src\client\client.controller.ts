import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Req,
  HttpException,
  UseInterceptors,
  UploadedFiles,
  UploadedFile,
  ParseFilePipe,
  Query,
} from "@nestjs/common";
import { ClientService } from "./client.service";
import { UpdateClientDto } from "./dto/update-client.dto";
import { Public } from "src/auth/decorators/public.decorator";
import { SignUpDto } from "./dto/signup.dto";
import { SignInDto } from "./dto/signin.dto";
import { ForgotPasswordDto } from "./dto/forgot-password.dto";
import { VerifyOtpDto } from "./dto/verify-otp.dto";
import { ResetPasswordDto } from "./dto/reset-password.dto";
import { ApiBearerAuth } from "@nestjs/swagger";
import { ChangePasswordDto } from "./dto/change-password.dto";
import {
  AnyFilesInterceptor,
  FileFieldsInterceptor,
  FileInterceptor,
  FilesInterceptor,
} from "@nestjs/platform-express";
import { QuestionDto } from "./dto/questions.dto";
import { GetClientsDto } from "./dto/get-clients.dto";

@Controller("client")
export class ClientController {
  constructor(private readonly clientService: ClientService) { }

  @Public()
  @HttpCode(HttpStatus.OK)
  @Post("signup")
  async signup(@Body() signUpDto: SignUpDto) {
    return await this.clientService.signUp(signUpDto);
  }

  @Public()
  @HttpCode(HttpStatus.OK)
  @Post("signin")
  signIn(@Body() signInDto: SignInDto) {
    return this.clientService.signIn(signInDto.email, signInDto.password);
  }

  @Public()
  @HttpCode(HttpStatus.OK)
  @Post("forgot-password")
  forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto) {
    return this.clientService.forgotPassword(forgotPasswordDto);
  }

  @Public()
  @HttpCode(HttpStatus.OK)
  @Post("verify-otp")
  verifyOtp(@Body() verifyOptDto: VerifyOtpDto) {
    return this.clientService.verifyOtp(verifyOptDto);
  }

  @Public()
  @HttpCode(HttpStatus.OK)
  @Post("reset-password")
  resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.clientService.resetPassword(resetPasswordDto);
  }

  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @Get("me")
  async getProfile(@Req() req) {
    const { user } = req;
    const res = await this.clientService.getProfile(user.email);

    return res;
  }
  @HttpCode(HttpStatus.OK)
  @Post("change-password")
  async changePassword(
    @Body() changePasswordDto: ChangePasswordDto,
    @Req() req
  ) {
    const getUser = await this.clientService.getProfile(req["user"]["email"]);
    if (!getUser) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: `user not found`,
        },
        HttpStatus.BAD_REQUEST
      );
    }
    const res = await this.clientService.changePassword(
      changePasswordDto,
      getUser
    );
    // const objectWithoutPassword = { ...res }
    // let { password, code, ...rest } = objectWithoutPassword?._doc

    return res;
  }
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @Patch(":id")
  async updateProfile(
    @Param("id") id: string,
    @Body() updateProfileDto: UpdateClientDto
  ) {
    const res = await this.clientService.update({ _id: id }, updateProfileDto);
    return res;
  }

  @Post("upload-photos")
  @UseInterceptors(AnyFilesInterceptor())
  async uploadImages(
    @UploadedFiles() files: Array<Express.Multer.File>,
    @Req() req
  ) {
    if (!files.length) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: `no files to upload`,
        },
        HttpStatus.BAD_REQUEST
      );
    }
    // loop on files to save to s3
    let photos = [];
    for (const file of files) {
      const uploadedFile = await this.clientService.uploadPhotos(
        file,
        req.user
      );
      photos.push({
        key: file.fieldname,
        value: uploadedFile.imageName,
        url: uploadedFile.url,
      });
    }
    const images = photos.map((ele) => {
      return { key: ele.key, value: ele.value };
    });
    await this.clientService.update(
      { _id: req.user.id },
      { step: 5, photos: images }
    );
    // await this.clientService.addDetails(req.user.id, {
    //   prompt: "Please upload you current photos to compare you weight progress",
    //   value: images,
    //   step: 7,
    //   questionType: "upload-photo",
    // });
    return photos;
  }
  @Post("/upload-image")
  @UseInterceptors(FileInterceptor("file"))
  uploadFile(
    @UploadedFile(
      new ParseFilePipe({
        // validators: [new MaxFileSizeValidator({ maxSize: 2000 })],
      })
    )
    file: Express.Multer.File,
    @Req() req
  ) {
    return this.clientService.uploadFile(file, req.user);
  }

  // @ApiBearerAuth()
  // @Post("/add-details/:id")
  // @HttpCode(HttpStatus.OK)
  // async addDetails(@Param("id") id: string, @Body() question: QuestionDto) {
  //   const res = await this.clientService.addDetails(id, question);
  //   return res;
  // }

  // GET /client => GET ALL CLIENTS WITH PAGINATION
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @Get("/")
  async getAllClients(@Query() filterDto: GetClientsDto) {
    return await this.clientService.findAll(filterDto);
  }

  @Get("/coach/:coachId")
  async getClients(
    @Param("coachId") coachId: string,
    @Query("searchKey") searchKey: string,
    @Query("sortKey") sortKey: string,
    @Query("sortOrder") sortOrder: string
  ) {
    const response = await this.clientService.clientsByCoach(
      coachId,
      searchKey,
      sortKey,
      sortOrder
    );
    return response;
  }

  @ApiBearerAuth()
  @Delete(":id")
  @HttpCode(HttpStatus.OK)
  async deleteClient(@Param("id") id: string) {
    const res = await this.clientService.deleteClient(id);
    return res;
  }

  @ApiBearerAuth()
  @Get("/archived-client/:id")
  @HttpCode(HttpStatus.OK)
  async archivedClient(@Param("id") id: string) {
    const res = await this.clientService.archivedClients(id);
    return res;
  }

  @ApiBearerAuth()
  @Get(":id")
  @HttpCode(HttpStatus.OK)
  async getById(@Param("id") id: string) {
    const res = await this.clientService.getById(id);
    return res;
  }

  @ApiBearerAuth()
  @Get("/meal-by-date/:id")
  @HttpCode(HttpStatus.OK)
  async getMealByDate(@Param("id") id: string, @Query("date") date: string) {
    const res = await this.clientService.getMealByDate(id, date);
    return res;
  }

  @ApiBearerAuth()
  @Get("/goal-by-date/:id")
  @HttpCode(HttpStatus.OK)
  async getGoalByDate(@Param("id") id: string, @Query("date") date: string) {
    const res = await this.clientService.getGoalByDate(id, date);
    return res;
  }

  @ApiBearerAuth()
  @Get("/client-goals/:id")
  @HttpCode(HttpStatus.OK)
  async getAllGoals(@Param("id") id: string) {
    const res = await this.clientService.getAllGoals(id);
    return res;
  }

  @ApiBearerAuth()
  @Get("/client-coach/:id")
  @HttpCode(HttpStatus.OK)
  async getClientCoach(@Param("id") id: string) {
    const res = await this.clientService.getClientCoach(id);
    return res;
  }

  @ApiBearerAuth()
  @Get("/client-docs/:id")
  @HttpCode(HttpStatus.OK)
  async clientDocs(@Param("id") id: string) {
    const res = await this.clientService.clientDocs(id);
    return res;
  }
}
