import { Body, Controller, Delete, Get, Param, Post, Put, Query } from '@nestjs/common';
import { WorkoutService } from './workout.service';
import { AddWorkoutDTO } from './dto/addWorkout.dto';
import { UpdateWorkoutDTO } from './dto/updateWorkout.dto';
import { FilterWorkoutDTO } from './dto/filterWorkout.dto';

@Controller('workout')
export class WorkoutController {
  constructor(private readonly workoutService: WorkoutService) { }

  // * POST -> /exercises
  @Post()
  async CreateExercise(
    @Body() body: AddWorkoutDTO
  ) {
    return await this.workoutService.create(body);
  }

  // * GET -> /exercises
  @Get()
  async GetAll(
    @Query() filters: FilterWorkoutDTO
  ) {
    return await this.workoutService.findAll(filters);
  }

  // * GET -> /exercises/:id
  @Get(":id")
  async GetById(
    @Param("id") id: string
  ) {
    return await this.workoutService.findById(id);
  }

  // * PUT -> /exercises/:id
  @Put(":id")
  async UpdateExercise(
    @Param("id") id: string,
    @Body() body: UpdateWorkoutDTO,
  ) {
    return await this.workoutService.updateById(id, body);
  }

  // * DELETE -> /exercises/:id
  @Delete(":id")
  async DeleteExercise(
    @Param("id") id: string,
  ) {
    return await this.workoutService.deleteById(id);
  }
}
