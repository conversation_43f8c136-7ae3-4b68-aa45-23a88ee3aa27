import { ApiProperty } from "@nestjs/swagger";
import { IsNumber, IsString } from "class-validator";

export class CreateGoalDto {
  @ApiProperty({ example: "200" })
  @IsNumber()
  calories: number;

  @ApiProperty({ example: "20g" })
  @IsNumber()
  proteins: number;

  @ApiProperty({ example: "30g" })
  @IsNumber()
  carbs: number;

  @ApiProperty({ example: "50g" })
  @IsNumber()
  fat: number;

  @ApiProperty({ example: "40%" })
  @IsNumber()
  proteinsPercentage: number;

  @ApiProperty({ example: "20%" })
  @IsNumber()
  carbsPercentage: number;

  @ApiProperty({ example: "10%" })
  @IsNumber()
  fatPercentage: number;

  @ApiProperty({ example: "1234566" })
  @IsString()
  client: string;
}
