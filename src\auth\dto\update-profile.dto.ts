import { ApiProperty, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsMongoId,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';

class License {
  @ApiProperty({ example: 'jon wick' })
  @IsString()
  name: string;

  @ApiProperty({ example: 'USA' })
  @IsString()
  country: string;

  @ApiProperty({ example: 'USA-123' })
  @IsString()
  licenseNumber: string;

  @ApiProperty({ example: 'url' })
  @IsString()
  driverLicenseFrontPhoto: string;

  @ApiProperty({ example: 'url' })
  @IsString()
  driverLicenseBackPhoto: string;

  @ApiProperty({ example: 'url' })
  @IsString()
  passportOrICFrontPhoto: string;

  @ApiProperty({ example: 'url' })
  @IsString()
  passportOrICBackPhoto: string;


}

export class UpdateProfileDto {
  
  @ApiProperty({ example: 'jon wick' })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ example: '*********' })
  @IsString()
  @IsOptional()
  phoneNumber?: string;

  @ApiProperty({ example: 'url' })
  @IsString()
  @IsOptional()
  photo?: string;

  @ApiProperty()
  @ValidateNested({ each: true })
  @IsObject()
  @IsOptional()
  @Type(() => License)
  license?: License;

  
  @ApiProperty()
  @IsOptional()
  @IsArray()
  @IsMongoId({
    each: true,
  })
  favoriteCars?: string[];

}
