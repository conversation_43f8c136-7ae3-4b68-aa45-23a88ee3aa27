import { UseGuards } from "@nestjs/common";
import {
  MessageBody,
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
  WsResponse,
} from "@nestjs/websockets";
import { Server } from "socket.io";
// import { AuthGuard } from '../auth/auth.guard';

@WebSocketGateway({
  cors: {
    origin: "*",
  },
})
export class ChatGateway {
  @WebSocketServer()
  server: Server;

  private connectedClients = new Map<string, string>();

  private getKeyByValue(value) {
    for (let [key, val] of this.connectedClients.entries()) {
      if (val === value) {
        return key;
      }
    }
  }

  // @SubscribeMessage('message')
  // handleMessage(client: any, payload: any): string {
  //   return 'Hello world!';
  // }

  // @SubscribeMessage('identity')
  // async identity(@MessageBody() data: number): Promise<any> {
  //   console.log("yyyy");
  //   // this.server.emit('message', data);
  //   (this.server as any).emit('message',"yooo");
  //   return data;
  // }

  // #region send user message

  afterInit() {
    console.log("WebSocketGateway initialized");
  }

  handleConnection(client: any, ...args: any[]) {
    this.connectedClients.set(client.handshake.query.userId, client.id);
    console.log(
      `Client ${client.handshake.query.userId} connected to WebSocketGateway`
    );
  }

  handleDisconnect(client: any) {
    console.log(
      `Client ${this.getKeyByValue(
        client.id
      )} disconnected from WebSocketGateway`
    );
    this.connectedClients.delete(this.getKeyByValue(client.id));
  }

  public sendToClient(clientId: string, event: string, data: any) {
    this.server.to(this.connectedClients.get(clientId)).emit(event, data);
  }
}
