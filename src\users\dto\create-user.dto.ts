// export class CreateUserDto {}
import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {
  IsString,
  IsEmail,
  IsOptional,
  IsArray,
  IsMongoId,
  IsBoolean,
} from "class-validator";

export class CreateUserDto {
  @ApiProperty({ example: "jon wick" })
  @IsString()
  name: string;

  @ApiProperty({ example: "<EMAIL>" })
  @IsEmail()
  email: string;

  @ApiProperty({ example: "jon wick" })
  @IsString()
  userType: string;

  @ApiProperty({ example: "jon wick" })
  @IsString()
  userId: string;
}
