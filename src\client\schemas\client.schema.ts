import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, SchemaFactory } from "@nestjs/mongoose";
import * as mongoose from "mongoose";
import { IClient } from "../interfaces/client.interface";
import { <PERSON> } from "src/coach/schemas/coach.schema";
import { Question } from "src/questions/schemas/question.schema";

@Schema({ timestamps: true })
export class Client extends mongoose.Document implements IClient {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true, unique: true, index: true })
  email: string;

  @Prop({ required: true })
  password: string;

  @Prop({ required: false, default: "" })
  code: string;

  @Prop({ required: false, default: 0 })
  verificationCodeExpiresAt: number;

  @Prop({ required: false, default: "" })
  referralCode: string;

  @Prop({ required: false, default: [] })
  photos: Array<{ key: string; value: string }>;

  @Prop({ required: false, default: "" })
  image: string;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: "Coach", required: false })
  coach: mongoose.Types.ObjectId | Coach;

  @Prop({ required: true, default: "" })
  userType: string;

  @Prop({ required: false, default: "" })
  roomId: string;

  @Prop({ required: false, default: false })
  archived: boolean;

  @Prop({ required: false, default: false })
  deleted: boolean;

  @Prop({ required: false, default: "" })
  dob: string;

  @Prop({ required: false, default: "" })
  gender: string;

  @Prop({ required: false, default: "" })
  height: string;

  @Prop({ required: false, default: "" })
  weight: string;

  @Prop({ required: false, default: "" })
  goal: string;

  @Prop({ required: false, default: "" })
  howActive: string;

  @Prop({ required: false, default: "" })
  goalWeight: string;

  @Prop({ required: false, default: 0 })
  step: number;
}

export const ClientSchema = SchemaFactory.createForClass(Client);
