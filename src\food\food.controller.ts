import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Req,
  Query,
} from "@nestjs/common";
import { FoodService } from "./food.service";
import { CreateFoodDto } from "./dto/create-food.dto";
import { UpdateFoodDto } from "./dto/update-food.dto";

@Controller("food")
export class FoodController {
  constructor(private readonly foodService: FoodService) {}

  @Post()
  create(@Body() createFoodDto: CreateFoodDto, @Req() req) {
    return this.foodService.create(createFoodDto, req.user);
  }

  @Get()
  findAll(@Query("searchKey") searchKey: string, @Req() req) {
    return this.foodService.findAll(searchKey, req.user);
  }

  @Get(":id")
  findOne(@Param("id") id: string) {
    return this.foodService.findOne(id);
  }

  @Patch(":id")
  update(@Param("id") id: string, @Body() updateFoodDto: UpdateFoodDto) {
    return this.foodService.update(id, updateFoodDto);
  }

  @Delete(":id")
  remove(@Param("id") id: string) {
    return this.foodService.removeFood(id);
  }
}
