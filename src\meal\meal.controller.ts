import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Req,
  Query,
} from "@nestjs/common";
import { MealService } from "./meal.service";
import { CreateMealDto } from "./dto/create-meal.dto";
import { UpdateMealDto } from "./dto/update-meal.dto";

@Controller("meal")
export class MealController {
  constructor(private readonly mealService: MealService) { }

  @Post()
  create(@Body() createMealDto: CreateMealDto, @Req() req) {
    return this.mealService.create(createMealDto, req.user);
  }

  @Get('/client/:id')
  findAll(@Req() req, @Query('date') date: string, @Param('id') id: string) {
    return this.mealService.findAll(req.user, id, date);
  }

  @Get("/coach/:id")
  getAllClientMealsByCoach(@Req() req, @Param("id") id: string) {
    return this.mealService.getAllClientMealsByCoach(id, req.query);
  }

  @Get(":id")
  findOne(@Param("id") id: string) {
    return this.mealService.findOne(id);
  }

  @Patch(":id")
  update(@Param("id") id: string, @Body() updateMealDto: UpdateMealDto) {
    return this.mealService.update(id, updateMealDto);
  }

  @Delete(":id")
  remove(@Param("id") id: string) {
    return this.mealService.remove(+id);
  }
}
