import { Injectable } from "@nestjs/common";
import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
  DeleteObjectCommand,
} from "@aws-sdk/client-s3";
const crypto = require("crypto");

import { ConfigService } from "@nestjs/config";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { UpdateUploadImageDto } from "./dto/update-upload-image.dto";
import { UsersService } from "src/users/users.service";
import { generateFileName } from "src/utils/comman";
// import { uploadImage } from './upload-image.helper'

@Injectable()
export class UploadImageService {
  private readonly s3Client = new S3Client({
    region: this.configService.getOrThrow("BUCKET_ORIGIN"),
    credentials: {
      accessKeyId: process.env.ACCESS_KEY,
      secretAccessKey: process.env.SECRET_KEY,
    },
  });
  constructor(private readonly configService: ConfigService) {}
  findAll() {
    return `This action returns all uploadImage`;
  }

  async getObjectSignedUrl(key: string) {
    const params = {
      Bucket: "plate-mate",
      Key: key,
    };

    // https://aws.amazon.com/blogs/developer/generate-presigned-url-modular-aws-sdk-javascript/
    const command = new GetObjectCommand(params);
    const seconds = 604800; //seconds in one week
    const url = await getSignedUrl(this.s3Client, command, {
      expiresIn: seconds,
    });

    return url;
  }

  async uploadFile(file: any, user: any, imageName: string) {
    const { mimetype, buffer } = file;
    const uploadParams = {
      Bucket: "plate-mate",
      Body: buffer,
      Key: imageName,
      ContentType: mimetype,
    };

    await this.s3Client.send(new PutObjectCommand(uploadParams));
    const url = await this.getObjectSignedUrl(imageName);
    return { url, imageName };
  }

  async deleteFile(filename: string) {
    try {
      return await this.s3Client.send(
        new DeleteObjectCommand({
          Bucket: "plate-mate",
          Key: filename,
        })
      );
    } catch (e: unknown) {
      console.log(`There was an error deleting file "${filename}" on S3`);
      throw e;
    }
  }

  findOne(id: number) {
    return `This action returns a #${id} uploadImage`;
  }

  update(id: number, updateUploadImageDto: UpdateUploadImageDto) {
    return `This action updates a #${id} uploadImage`;
  }

  remove(id: number) {
    return `This action removes a #${id} uploadImage`;
  }

  async uploadPhotos(file: any, user: any) {
    const imageName = generateFileName();
    const imageData = await this.uploadFile(file, user, imageName);
    return imageData;
  }
}
