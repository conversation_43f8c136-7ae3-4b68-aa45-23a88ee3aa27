import { Body, Controller, Delete, Get, NotFoundException, Param, Patch, Post } from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { CreateNotificationDTO } from './dto/create-notification.dto';
import { UpdateNotificationDto } from './dto/update-notification.dto';

@Controller('notifications')
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) { }

  // * POST -> /notifications
  @Post()
  async createNotification(
    @Body() createNotificationDto: CreateNotificationDTO,
  ) {
    const notification = await this.notificationsService.create(
      createNotificationDto,
    );
    return notification;
  }

  // * GET -> /notifications
  @Get()
  async findAll() {
    return this.notificationsService.findAll();
  }

  // * GET -> /notifications/user/:userId
  @Get('user/:userId')
  async findAllNotificationsByUserId(@Param('userId') userId: string) {
    const notification = await this.notificationsService.findAllNotificationsByUserId(userId);
    return {
      data: notification,
    };
  }

  // * PUT -> /notifications/:notificationId
  @Patch(':notificationId')
  async updateOne(
    @Param('notificationId') notificationId: string,
    @Body() UpdateNotificationDto: UpdateNotificationDto,
  ) {
    const notification = await this.notificationsService.updateOne(notificationId, UpdateNotificationDto);
    if (!notification) {
      throw new NotFoundException('notification with this id is not found');
    } else {
      return notification;
    }
  }

  // * DELETE -> /notifications/:notificationId
  @Delete(':notificationId')
  async deleteOne(
    @Param('notificationId') notificationId: string,
  ) {
    const notification = await this.notificationsService.deleteOne(
      notificationId,
    );
    if (!notification) {
      throw new NotFoundException('notification with this id is not found');
    }
    return {
      message: 'notification deleted successfully',
    };
  }
}
