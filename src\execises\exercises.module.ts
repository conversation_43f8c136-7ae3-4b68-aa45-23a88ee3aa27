import { Module } from '@nestjs/common';

import { MongooseModule } from '@nestjs/mongoose';
import { Exercises, ExercisesSchema } from './schema/exercises.schema';
import { ExercisesController } from './exercises.controller';
import { ExercisesService } from './exercises.service';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Exercises.name, schema: ExercisesSchema, collection: "exercises", }]),
  ],
  controllers: [ExercisesController],
  providers: [ExercisesService]
})
export class ExercisesModule { }
