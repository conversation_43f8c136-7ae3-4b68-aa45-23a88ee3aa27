import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Notifications } from './schema/notification.schema';
import { Model } from 'mongoose';
import { FirebaseService } from '../firebase/firebase.service';
import { CreateNotificationDTO } from './dto/create-notification.dto';
import { UpdateNotificationDto } from './dto/update-notification.dto';

@Injectable()
export class NotificationsService {
  constructor(
    @InjectModel(Notifications.name)
    private readonly notificationModel: Model<Notifications>,
    private readonly firebaseService: FirebaseService,
  ) { }

  async create(createNotificationDto: CreateNotificationDTO) {
    const notification = await this.notificationModel.create(
      createNotificationDto,
    );
    this.sendNotificationWithFireBase([createNotificationDto.fcmToken], createNotificationDto.title, createNotificationDto.body);

    return notification;
  }

  async findAllNotificationsByUserId(user: string) {
    const unreadCount = await this.notificationModel.countDocuments({ user, read: false });
    const notifications = await this.notificationModel.find({ user }).sort({ createdAt: -1 }).exec();
    return { unreadCount, notifications };
  }

  async findAll() {
    const notifications = await this.notificationModel.find().sort({ createdAt: -1 }).exec();
    return notifications;
  }

  async updateOne(
    notificationId: string,
    UpdateNotificationDto: UpdateNotificationDto,
  ) {
    const notification = await this.notificationModel.findOneAndUpdate(
      { _id: notificationId },
      { $set: UpdateNotificationDto },
      { new: true },
    ).exec();
    return notification;
  }

  async updateAll(user: string) {
    const notifications = await this.notificationModel.updateMany(
      { user, read: false },
      { $set: { read: true } },
      { new: true },
    ).exec();

    if (!notifications) {
      throw new NotFoundException(
        'No notifications found for this user',
      );
    }

    return this.notificationModel.find({ user }).exec();
  }

  async deleteOne(notificationId: string) {
    return await this.notificationModel.findOneAndDelete({
      _id: notificationId,
    });
  }
  async sendNotificationWithFireBase(
    tokens: string[],
    title: string,
    body: string,
    data?: any,
  ) {
    this.firebaseService.sendPushNotification(tokens, title, body, data);
  }
}
