// export class CreateUserDto {}
import { ApiProperty } from "@nestjs/swagger";
import {
  IsString,
  IsEmail,
  IsOptional,
  isString,
  IsBoolean,
  IsNumber,
} from "class-validator";

export class SignUpDto {
  @ApiProperty({ example: "jon wick" })
  @IsString()
  name: string;

  @ApiProperty({ example: "jon wick" })
  @IsString()
  userType: string;

  @ApiProperty({ example: "<EMAIL>" })
  @IsEmail()
  email: string;

  @ApiProperty({ example: "causewaysecret" })
  @IsString()
  password: string;

  @ApiProperty({ example: "##123456" })
  @IsOptional()
  @IsString()
  referralCode: string;

  @ApiProperty({ example: "coachid" })
  @IsOptional()
  @IsString()
  coach: string;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  archived: boolean;

  @ApiProperty({ example: "32" })
  @IsOptional()
  @IsString()
  dob?: string;

  @ApiProperty({ example: "male" })
  @IsOptional()
  @IsString()
  gender?: string;

  @ApiProperty({ example: "5'9'" })
  @IsOptional()
  @IsString()
  height?: string;

  @ApiProperty({ example: "123456" })
  @IsOptional()
  @IsString()
  weight?: string;

  @ApiProperty({ example: "123456" })
  @IsOptional()
  @IsString()
  goal?: string;

  @ApiProperty({ example: "Lightly Active" })
  @IsOptional()
  @IsString()
  howActive?: string;

  @ApiProperty({ example: "Lose Weight" })
  @IsOptional()
  @IsString()
  goalWeight?: string;

  @ApiProperty({ example: 1 })
  @IsOptional()
  @IsNumber()
  step: number;
}
