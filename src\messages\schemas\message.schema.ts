import { <PERSON><PERSON><PERSON>, <PERSON>p, SchemaFactory } from '@nestjs/mongoose';
import * as mongoose from 'mongoose';
import {
  IMessage
} from '../interfaces/messages.interface';

@Schema({ timestamps: true })
export class Message extends mongoose.Document implements IMessage {

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Chat', required: true })
  chatId: string;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true })
  sender: string;

  @Prop({ required: true })
  content: string;

  @Prop({ default: false })
  read: boolean;

}

export const MessageSchema = SchemaFactory.createForClass(Message);




