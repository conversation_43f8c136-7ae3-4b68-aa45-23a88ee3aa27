import { Injectable } from "@nestjs/common";
import { CreateFoodDto } from "./dto/create-food.dto";
import { UpdateFoodDto } from "./dto/update-food.dto";
import { InjectModel } from "@nestjs/mongoose";
import { Food } from "./schemas/food.schema";
import { Model } from "mongoose";

type QueryType = {
  deleted: boolean;
  $and: Array<
    | {
      $or?: Array<{
        addedByAdmin?: boolean;
        client?: string;
        name?: { $regex: string; $options: string };
      }>;
    }
    | { client: string }
  >;
};
@Injectable()
export class FoodService {
  constructor(@InjectModel(Food.name) private foodModel: Model<Food>) { }
  create(createFoodDto: CreateFoodDto, user: any) {
    return this.foodModel.create({
      ...createFoodDto,
      client: user.id,
      createdAt: new Date().valueOf(),
      updatedAt: new Date().valueOf(),
    });
  }

  async findAll(searchKey: string, user: any) {
    const query: any = {
      deleted: false,
    };

    if (searchKey) {
      query.$or = [
        { name: { $regex: searchKey, $options: "i" }, client: user.id },
        { name: { $regex: searchKey, $options: "i" }, addedByAdmin: true },
      ];
    } else {
      {
        query.$or = [{ addedByAdmin: true }, { client: user.id }];
      }
    }

    const data = await this.foodModel.find(query).sort({ createdAt: -1 });
    return { foods: data, count: data.length };
  }

  findOne(id: string) {
    return this.foodModel.findOne({ _id: id });
  }

  findByWhere(where: object) {
    return this.foodModel.find(where);
  }

  update(id: string, updateFoodDto: UpdateFoodDto) {
    return this.foodModel.updateOne({ _id: id }, updateFoodDto);
  }

  removeFood(id: string) {
    return this.foodModel.findOneAndDelete({ _id: id });
  }
}
