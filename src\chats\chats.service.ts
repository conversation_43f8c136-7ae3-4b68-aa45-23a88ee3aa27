import { Injectable } from "@nestjs/common";
import { CreateChatDto } from "./dto/create-chat.dto";
import { UpdateChatDto } from "./dto/update-chat.dto";
import { Chat } from "./schemas/chat.schema";
import { Model, SortOrder } from "mongoose";
import { InjectModel } from "@nestjs/mongoose";
import { IChat } from "./interfaces/chats.interface";
import { PageOptionsDto } from "../utils/classes/page-options.class";
import { join } from "path";
import { PagedData } from "../utils/classes/paged-data.class";
import { Client } from "src/client/schemas/client.schema";
import { Coach } from "src/coach/schemas/coach.schema";
import { UploadImageService } from "src/upload-image/upload-image.service";
import { Message } from "src/messages/schemas/message.schema";

@Injectable()
export class ChatsService {
  constructor(
    @InjectModel(Chat.name) private readonly chatModel: Model<Chat>,
    @InjectModel(Client.name) private readonly clientModel: Model<Client>,
    @InjectModel(Coach.name) private readonly coachModel: Model<Coach>,
    @InjectModel(Message.name) private readonly msgModel: Model<Message>,
    private readonly uploadImageService: UploadImageService
  ) { }

  async create(chatBody: IChat) {
    const chat = await this.chatModel.create(chatBody);
    // .populate({ path: 'users', select: '-password -license -favoriteCars' })
    // .populate({ path: 'lastMessage' })

    return chat;
  }

  async findByIds(ids: string[]): Promise<(any | any)[]> {
    const [firstId, secondId] = ids;

    const [firstClient, firstCoach, secondClient, secondCoach] =
      await Promise.all([
        this.clientModel.findById(firstId).exec(),
        this.coachModel.findById(firstId).exec(),
        this.clientModel.findById(secondId).exec(),
        this.coachModel.findById(secondId).exec(),
      ]);

    const results: (Client | Coach)[] = [];
    if (firstClient) results.push(firstClient);
    if (firstCoach) results.push(firstCoach);
    if (secondClient) results.push(secondClient);
    if (secondCoach) results.push(secondCoach);

    return results;
  }

  async countUnreadMessages(chatId: string): Promise<number> {
    const result = await this.msgModel.find({
      chatId,
      read: false,
    });

    return result.length;
  }

  async findAll(queryParams: PageOptionsDto, filterOptions?: any) {
    const sort = { [queryParams.sortField]: queryParams.order as SortOrder };
    const filters = filterOptions.filters ? filterOptions.filters : {};
    const query = this.chatModel
      .find(filters)
      .populate({
        path: "lastMessage",
      })
      .skip(queryParams.skip);
    if (queryParams.limit) {
      query.limit(queryParams.limit);
    }
    if (queryParams.sortField && queryParams.order) {
      query.sort(sort);
    } else {
      query.sort({ updatedAt: "desc" });
    }
    if (filterOptions.fields) {
      query.select(filterOptions.fields.split(",").join(" "));
    }

    const results = await query;
    const data = [];
    for (let result of results) {
      result = result.toObject();
      let users = await this.findByIds(result.users);
      let unReadMsgCount = await this.countUnreadMessages(result._id);
      let usersData = await Promise.all(
        users.map(async (user) => {
          if (user.userType === "coach" && user.photo) {
            user.photo = await this.uploadImageService.getObjectSignedUrl(
              user.photo
            );
          } else if (user.image) {
            user.image = await this.uploadImageService.getObjectSignedUrl(
              user.image
            );
          }
          return user;
        })
      );
      data.push({ ...result, users: usersData, unReadMsgCount });
    }

    const count: number = await this.chatModel.countDocuments(filters);
    return new PagedData(data, count);
  }

  async findOne(filter: any) {
    let data = await this.chatModel
      .findOne(filter)
      .populate({
        path: "lastMessage",
      })
      .exec();
    if (!data) return data;
    data = data.toObject();
    const users: any = await this.findByIds(data.users);

    data["users"] = users;
    return data;
  }

  async update(filter: any, updateCarDto: any) {
    let data = await this.chatModel
      .findOneAndUpdate(filter, updateCarDto, { new: true })
      .populate({
        path: "lastMessage",
      })
      .exec();
    if (!data) return data;
    data = data.toObject();
    const users: any = await this.findByIds(data.users);

    data["users"] = users;
    return data;
  }

  async remove(filter: any) {
    return await this.chatModel.findOneAndDelete(filter).exec();
  }
}
