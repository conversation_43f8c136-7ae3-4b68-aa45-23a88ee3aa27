import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  NotFoundException,
} from "@nestjs/common";
import { Document, Model } from "mongoose";
import { ReviewService } from "./review.service";
import { CreateReviewDto } from "./dto/create-review.dto";
import { UpdateReviewDto } from "./dto/update-review.dto";

@Controller("review")
export class ReviewController {
  constructor(private readonly reviewService: ReviewService) {}

  @Post()
  create(@Body() createReviewDto: CreateReviewDto) {
    return this.reviewService.create(createReviewDto);
  }

  @Get("/")
  findOneReview(
    @Query("coachId") coachId: string,
    @Query("clientId") clientId: string,
    @Query("mealId") reviewFor: string
  ) {
    return this.reviewService.findOneReview(coachId, clientId, reviewFor);
  }

  // @Get(":id")
  // findOne(@Param("id") id: string) {
  //   return this.reviewService.findOne({});
  // }

  @Patch(":id")
  update(@Param("id") id: string, @Body() updateReviewDto: UpdateReviewDto) {
    return this.reviewService.update(id, updateReviewDto);
  }

  @Delete(":id")
  async remove(@Param("id") id: string) {
    const res = await this.reviewService.remove(id);
    if (!res) {
      throw new NotFoundException(`Offer with ID ${id} not found`);
    }
    return res;
  }
}
