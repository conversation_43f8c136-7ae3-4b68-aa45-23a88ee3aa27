import { Injectable } from "@nestjs/common";
import { CreateWeightDto } from "./dto/create-weight.dto";
import { UpdateWeightDto } from "./dto/update-weight.dto";
import { InjectModel } from "@nestjs/mongoose";
import { Weight } from "./schemas/weight.schema";
import { Model } from "mongoose";
import { generateFileName } from "src/utils/comman";
import { UploadImageService } from "src/upload-image/upload-image.service";
import { ClientService } from "src/client/client.service";

@Injectable()
export class WeightService {
  constructor(
    @InjectModel(Weight.name) private weightModel: Model<Weight>,
    private readonly uploadImageService: UploadImageService,
    private readonly clientService: ClientService
  ) {}
  create(createWeightDto: CreateWeightDto, user: any) {
    return this.weightModel.create({
      ...createWeightDto,
      client: user.id,
      createdAt: new Date().valueOf(),
      updatedAt: new Date().valueOf(),
      type: "weight",
    });
  }

  extractWeightUnit(data: any) {
    // Check if 'weight' key exists in data
    if (data.weight) {
      let weightString = data["weight"].trim();

      // Regular expression to match a number followed by optional space and 'kg' or 'lb'
      let match = weightString.match(/^(\d+)\s*(kg|lb)?$/);

      if (match) {
        let weight = match[1];
        let unit = match[2]; // Unit will be 'kg', 'lb', or undefined if not specified

        // Normalize unit to lowercase for consistency
        if (unit) {
          unit = unit.toLowerCase();
        } else {
          // Default to 'kg' if no unit specified
          unit = "kg";
        }

        return { weight: weight, unit: unit };
      } else {
        // Handle cases where the format doesn't match expected pattern
        return { weight: "", unit: "" };
      }
    } else {
      // Handle cases where 'weight' key is missing
      return { weight: "", unit: "" };
    }
  }

  async findAll(clientId: string) {
    const existingSignupWeight = await this.findOneWhere({
      client: clientId,
      type: "signup",
    });
    if (!existingSignupWeight) {
      const clinetWeightPhotos = await this.clientService.findOne({
        _id: clientId,
      });
      let clientAddedData: any = await this.clientService.findWeightData(
        clientId
      );
      const weightUnitData = this.extractWeightUnit(clientAddedData);
      const obj = {
        client: clientId,
        createdAt: new Date().valueOf(),
        updatedAt: new Date().valueOf(),
        value: weightUnitData?.weight,
        unit: weightUnitData?.unit,
        photos: clinetWeightPhotos?.photos,
        type: "signup",
      };
      await this.createFromSignupData(obj);
    }

    let weights: any = await this.weightModel.find({ client: clientId });
    let photos = [];
    let data = [];
    if (weights && weights.length) {
      for (const weight of weights) {
        if (weight.photos.length) {
          for (const photo of weight.photos) {
            const url = await this.uploadImageService.getObjectSignedUrl(
              photo.value
            );
            photos.push({ ...photo, url });
          }
          weight.photos = photos;
          data.push(weight);
        } else {
          data.push(weight);
        }
      }
    }
    return data;
  }

  findOne(id: string) {
    return this.weightModel.findOne({ _id: id });
  }

  update(id: string, updateWeightDto: UpdateWeightDto) {
    return this.weightModel.findOneAndUpdate({ _id: id }, updateWeightDto, {
      new: true,
    });
  }

  remove(id: string) {
    return this.weightModel.findOneAndDelete({ _id: id });
  }

  findOneWhere(where: object) {
    return this.weightModel.findOne(where);
  }

  async createFromSignupData(obj: object) {
    return this.weightModel.create(obj);
  }
}
