import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Req,
  HttpException,
  Query,
  UseInterceptors,
  UploadedFile,
  ParseFilePipe,
} from "@nestjs/common";
import { ApiBearerAuth } from "@nestjs/swagger";
import { CoachService } from "./coach.service";
import { CreateCoachDto } from "./dto/create-coach.dto";
import { UpdateCoachDto } from "./dto/update-coach.dto";
import { Public } from "src/auth/decorators/public.decorator";
import { SignInDto } from "./dto/signin.dto";
import { SignUpDto } from "./dto/signup.dto";
import { ForgotPasswordDto } from "./dto/forgot-password.dto";
import { VerifyOtpDto } from "./dto/verify-otp.dto";
import { ResetPasswordDto } from "./dto/reset-password.dto";
import { ChangePasswordDto } from "./dto/change-password.dto";
import { FileInterceptor } from "@nestjs/platform-express";
import { GetCoachesDto } from "./dto/get-coaches.dto";

@Controller("coach")
export class CoachController {
  constructor(private readonly coachService: CoachService) { }

  @Public()
  @HttpCode(HttpStatus.OK)
  @Post("signin")
  signIn(@Body() signInDto: SignInDto) {
    return this.coachService.signIn(signInDto.email, signInDto.password);
  }

  @Public()
  @HttpCode(HttpStatus.OK)
  @Post("signup")
  async signup(@Body() signUpDto: SignUpDto) {
    return await this.coachService.signUp(signUpDto);
  }

  @Public()
  @HttpCode(HttpStatus.OK)
  @Post("forgot-password")
  forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto) {
    return this.coachService.forgotPassword(forgotPasswordDto);
  }

  @Public()
  @HttpCode(HttpStatus.OK)
  @Post("verify-otp")
  verifyOtp(@Body() verifyOptDto: VerifyOtpDto) {
    return this.coachService.verifyOtp(verifyOptDto);
  }

  @Public()
  @HttpCode(HttpStatus.OK)
  @Post("reset-password")
  resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.coachService.resetPassword(resetPasswordDto);
  }

  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @Get("me")
  async getProfile(@Req() req) {
    const { user } = req;
    const res = await this.coachService.getProfile(user.email);
    return res;
  }

  @HttpCode(HttpStatus.OK)
  @Post("change-password")
  async changePassword(
    @Body() changePasswordDto: ChangePasswordDto,
    @Req() req
  ) {
    const getUser = await this.coachService.getProfile(req["user"]["email"]);
    if (!getUser) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: `user not found`,
        },
        HttpStatus.BAD_REQUEST
      );
    }
    const res = await this.coachService.changePassword(
      changePasswordDto,
      getUser
    );
    // const objectWithoutPassword = { ...res }
    // let { password, code, ...rest } = objectWithoutPassword?._doc

    return res;
  }

  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @Patch(":id")
  async updateProfile(
    @Param("id") id: string,
    @Body() updateProfileDto: UpdateCoachDto
  ) {
    const res = await this.coachService.update({ _id: id }, updateProfileDto);
    return res;
  }

  @Post("/upload-image")
  @UseInterceptors(FileInterceptor("file"))
  uploadFile(
    @UploadedFile(
      new ParseFilePipe({
        // validators: [new MaxFileSizeValidator({ maxSize: 2000 })],
      })
    )
    file: Express.Multer.File,
    @Req() req
  ) {
    return this.coachService.uploadFile(file, req.user);
  }

  // GET /coaches => GET ALL COACHES WITH PAGINATION
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @Get("/")
  async getCoaches(@Query() filterDto: GetCoachesDto) {
    return await this.coachService.findAll(filterDto);
  }

}
