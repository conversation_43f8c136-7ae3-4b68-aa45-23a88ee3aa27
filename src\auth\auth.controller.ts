import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Delete,
  HttpStatus,
  HttpCode,
  Query,
  Req,
  HttpException,
} from "@nestjs/common";
import { AuthService } from "./auth.service";
import { Public } from "./decorators/public.decorator";
import { SignUpDto } from "./dto/signup.dto";
import { SignInDto } from "./dto/signin.dto";
import { ApiBearerAuth, ApiTags } from "@nestjs/swagger";
import { ForgotPasswordDto } from "./dto/forgot-password.dto";
import { VerifyOptDto } from "./dto/verify-otp.dto";
import { ResetPasswordDto } from "./dto/reset-password-otp.dto";
import { UpdateProfileDto } from "./dto/update-profile.dto";
import { ChangePasswordDto } from "src/client/dto/change-password.dto";

@ApiTags("Auth")
@Controller("auth")
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Public()
  @HttpCode(HttpStatus.OK)
  @Post("signin")
  signIn(@Body() signInDto: SignInDto) {
    return this.authService.signIn(signInDto.email, signInDto.password);
  }

  // @Public()
  // @HttpCode(HttpStatus.OK)
  // @Post('signup')
  // async signup (@Body() signUpDto: SignUpDto) {
  //   return await this.authService.signUp(signUpDto)
  // }

  @Public()
  @HttpCode(HttpStatus.OK)
  @Post("forgot-password")
  forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto) {
    return this.authService.forgotPassword(forgotPasswordDto);
  }

  @Public()
  @HttpCode(HttpStatus.OK)
  @Post("verify-otp")
  verifyOtp(@Body() verifyOptDto: VerifyOptDto) {
    return this.authService.verifyOtp(verifyOptDto);
  }

  @Public()
  @HttpCode(HttpStatus.OK)
  @Post("reset-password")
  resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.authService.resetPassword(resetPasswordDto);
  }

  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @Get("me")
  async getProfile(@Req() req) {
    const res = await this.authService.getProfile(req["user"]["sub"]);
    const { password, ...profile } = res;
    return profile;
  }

  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @Patch("me")
  async updateProfile(@Body() updateProfileDto: UpdateProfileDto, @Req() req) {
    const res = await this.authService.updateProfile(
      req["user"]["sub"],
      updateProfileDto
    );
    const { password, ...profile } = res;
    return profile;
  }

  // @ApiBearerAuth()
  // @HttpCode(HttpStatus.OK)
  // @Delete("me")
  // async delete(@Req() req) {
  //   const res = await this.authService.deleteAccount(req["user"]["sub"]);
  //   return res;
  // }

  @HttpCode(HttpStatus.OK)
  @Post("change-password")
  async changePassword(
    @Body() changePasswordDto: ChangePasswordDto,
    @Req() req
  ) {
    const getUser = await this.authService.updateUserPass(
      req.user,
      changePasswordDto
    );
    if (!getUser) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: `user not found`,
        },
        HttpStatus.BAD_REQUEST
      );
    }

    return getUser;
  }
}
