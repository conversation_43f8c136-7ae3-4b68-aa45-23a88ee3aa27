import { <PERSON>p, Schema, SchemaFactory } from "@nestjs/mongoose";
import mongoose from "mongoose";
import { Client } from "src/client/schemas/client.schema";
import { Coach } from "src/coach/schemas/coach.schema";

@Schema({ timestamps: false })
export class Goal {
  @Prop({ required: true })
  calories: number;

  @Prop({ required: true })
  proteins: number;

  @Prop({ required: true })
  carbs: number;

  @Prop({ required: true })
  fat: number;

  @Prop({ required: true })
  proteinsPercentage: number;

  @Prop({ required: true })
  carbsPercentage: number;

  @Prop({ required: true })
  fatPercentage: number;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: "Coach",
    required: true,
  })
  coach: mongoose.Types.ObjectId | Coach;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: "Client",
    required: true,
  })
  client: mongoose.Types.ObjectId | Client;

  @Prop({ required: false })
  createdAt: string;

  @Prop({ required: false })
  updatedAt: string;
}
export const GoalSchema = SchemaFactory.createForClass(Goal);
