import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, SchemaFactory } from "@nestjs/mongoose";
import * as mongoose from "mongoose";
import { User } from "src/users/schemas/user.schema";

@Schema({ timestamps: true })
export class Question extends mongoose.Document {
  @Prop({ required: true })
  prompt: string;

  @Prop({ required: true })
  value: string[];

  @Prop({ required: true })
  step: number;

  @Prop({ required: true })
  questionType: string;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: "User", required: true })
  // user: string
  user: mongoose.Types.ObjectId | User;
}

export const QuestionSchema = SchemaFactory.createForClass(Question);
