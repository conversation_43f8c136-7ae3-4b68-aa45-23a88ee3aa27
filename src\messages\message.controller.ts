import {
  <PERSON>,
  Get,
  Post,
  Body,
  <PERSON>,
  Param,
  Delete,
  Req,
  Query,
} from "@nestjs/common";
import { MessagesService } from "./messages.service";

@Controller("message")
export class MessageController {
  constructor(private readonly messageService: MessagesService) {}

  @Patch(":id")
  updateMessageStatus(@Param("id") id: string, @Body() updateDto: any) {
    return this.messageService.update(id, updateDto);
  }
}
