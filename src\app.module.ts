import { Module } from "@nestjs/common";
import { AppController } from "./app.controller";
import { AppService } from "./app.service";
import { UsersModule } from "./users/users.module";
import { MongooseModule } from "@nestjs/mongoose";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { AuthModule } from "./auth/auth.module";
import { MailerModule } from "@nestjs-modules/mailer";
import { CoachModule } from "./coach/coach.module";
import { UploadImageModule } from "./upload-image/upload-image.module";
import { ClientModule } from "./client/client.module";
import { QuestionsModule } from "./questions/questions.module";
import { InvitesModule } from "./invites/invites.module";
// import { ChatModule } from "./chat/chat.module";
import { DocumentModule } from "./document/document.module";
import { SubscriptionModule } from "./subscription/subscription.module";
import { ReviewModule } from "./review/review.module";
import { FoodModule } from "./food/food.module";
import { MealModule } from "./meal/meal.module";
import { GoalModule } from "./goal/goal.module";
import { ChatsModule } from "./chats/chats.module";
import { MessagesModule } from "./messages/messages.module";
import { WeightModule } from './weight/weight.module';
import { WaterIntakeModule } from './water-intake/water-intake.module';
import { NotificationsModule } from './notifications/notifications.module';
import { ExercisesModule } from './execises/exercises.module';
import { WorkoutModule } from './workout/workout.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        uri: configService.get<string>("MONGODB_URL"),
      }),
      inject: [ConfigService],
    }),
    MailerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        transport: {
          host: configService.get<string>("MAIL_HOST"),
          port: configService.get<number>("MAIL_PORT"),
          secure: false,
          auth: {
            user: configService.get<string>("MAIL_USERNAME"),
            pass: configService.get<string>("MAIL_PASSWORD"),
          },
          // auth: {
          //   user: 'apikey',
          //   pass: configService.get<string>('SENDGRID_API_KEY'),
          // },
        },
        defaults: {
          from: `Plate Mate <${configService.get<string>("MAIL_FROM")}>`,
        },
      }),
      inject: [ConfigService],
    }),

    UsersModule,
    AuthModule,
    CoachModule,
    UploadImageModule,
    ClientModule,
    QuestionsModule,
    InvitesModule,
    ChatsModule,
    DocumentModule,
    SubscriptionModule,
    ReviewModule,
    FoodModule,
    MealModule,
    GoalModule,
    MessagesModule,
    WeightModule,
    WaterIntakeModule,
    NotificationsModule,
    ExercisesModule,
    WorkoutModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule { }
