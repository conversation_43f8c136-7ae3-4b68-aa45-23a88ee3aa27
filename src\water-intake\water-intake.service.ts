import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateWaterIntakeDto } from './dto/create-water-intake.dto';
import { UpdateWaterIntakeDto } from './dto/update-water-intake.dto';
import { InjectModel } from '@nestjs/mongoose';
import { WaterIntake } from './schemas/water-intake.schema';
import { Model } from 'mongoose';
import moment from '../utils/moment';

@Injectable()
export class WaterIntakeService {
  constructor(
    @InjectModel(WaterIntake.name) private waterIntakeModel: Model<WaterIntake>
  ) {}
  create(createWaterIntakeDto: CreateWaterIntakeDto, user: any) {
    return this.waterIntakeModel.create({
      ...createWaterIntakeDto,
      client: user.id,
      createdAt: new Date().valueOf(),
      updatedAt: new Date().valueOf(),
    });
  }

  findAll(clinetId: string) {
    return this.waterIntakeModel.find({ client: clinetId });
  }

  findOne(id: string) {
    return this.waterIntakeModel.findOne({ _id: id });
  }

  update(id: string, updateWaterIntakeDto: UpdateWaterIntakeDto) {
    return this.waterIntakeModel.findOneAndUpdate(
      { _id: id },
      updateWaterIntakeDto,
      {
        new: true,
      }
    );
  }

  remove(id: string) {
    return this.waterIntakeModel.findOneAndDelete({ _id: id });
  }

  findOneByDate(user: any, date: string) {
    if (!date) throw new NotFoundException('date required');

    const startOfDay = moment(date).startOf('day').valueOf(); // Start of day in milliseconds

    const endOfDay = moment(date).endOf('day').valueOf(); // End of day in milliseconds

    return this.waterIntakeModel.find({
      client: user.id,
      createdAt: { $gte: startOfDay, $lte: endOfDay },
    });
  }
}
