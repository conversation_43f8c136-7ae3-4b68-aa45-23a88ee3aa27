import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseInterceptors,
  UploadedFile,
  Req,
  MaxFileSizeValidator,
  ParseFilePipe,
  UploadedFiles,
  HttpException,
  HttpStatus,
} from "@nestjs/common";
import { UploadImageService } from "./upload-image.service";
import { CreateUploadImageDto } from "./dto/create-upload-image.dto";
import { UpdateUploadImageDto } from "./dto/update-upload-image.dto";
import { AnyFilesInterceptor, FileInterceptor } from "@nestjs/platform-express";
import { Public } from "src/auth/decorators/public.decorator";
import { generateFileName } from "src/utils/comman";

@Controller("upload-image")
export class UploadImageController {
  constructor(private readonly uploadImageService: UploadImageService) {}

  @Post("/upload")
  @UseInterceptors(FileInterceptor("file"))
  uploadFile(
    @UploadedFile(
      new ParseFilePipe({
        // validators: [new MaxFileSizeValidator({ maxSize: 2000 })],
      })
    )
    file: Express.Multer.File,
    @Req() req
  ) {
    const imageName = generateFileName();
    return this.uploadImageService.uploadFile(file, req.user, imageName);
  }

  @Get()
  findAll() {
    return this.uploadImageService.findAll();
  }

  @Get(":id")
  findOne(@Param("id") id: string) {
    return this.uploadImageService.findOne(+id);
  }

  @Patch(":id")
  update(
    @Param("id") id: string,
    @Body() updateUploadImageDto: UpdateUploadImageDto
  ) {
    return this.uploadImageService.update(+id, updateUploadImageDto);
  }

  @Delete(":id")
  remove(@Param("id") id: string) {
    return this.uploadImageService.remove(+id);
  }

  @Post("/delete-aws-file")
  deleteFileAWS(@Body() payload: any) {
    return this.uploadImageService.deleteFile(payload.fileName);
  }

  @Post("photos")
  @UseInterceptors(AnyFilesInterceptor())
  async uploadImages(
    @UploadedFiles() files: Array<Express.Multer.File>,
    @Req() req
  ) {
    if (!files.length) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: `no files to upload`,
        },
        HttpStatus.BAD_REQUEST
      );
    }
    // loop on files to save to s3
    let photos = [];
    for (const file of files) {
      const uploadedFile = await this.uploadImageService.uploadPhotos(
        file,
        req.user
      );
      photos.push({
        key: file.fieldname,
        value: uploadedFile.imageName,
        url: uploadedFile.url,
      });
    }

    return photos;
  }
}
