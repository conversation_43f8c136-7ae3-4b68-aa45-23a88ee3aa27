import { ApiProperty } from "@nestjs/swagger";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, IsString } from "class-validator";

export class CreateSubscriptionDto {
  @ApiProperty({ example: "basic" })
  @IsString()
  title: string;

  @ApiProperty({ example: ["service1", "service2"] })
  @IsArray()
  services: string[];

  @ApiProperty({ example: "1" })
  @IsNumber()
  validity: number;

  @ApiProperty({ example: "$20" })
  @IsString()
  price: string;

  @ApiProperty({ example: "month" })
  @IsString()
  plan: string;
}
