import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from "@nestjs/mongoose";
import mongoose from "mongoose";
import { Client } from "src/client/schemas/client.schema";

@Schema({ timestamps: false })
export class Weight {
  @Prop({ required: true })
  value: string;

  @Prop({ required: true })
  unit: string;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: "Client",
    required: true,
  })
  client: mongoose.Types.ObjectId | Client;

  @Prop({ required: false, default: [] })
  photos: Array<{ key: string; value: string }>;

  @Prop({ required: false })
  createdAt: string;

  @Prop({ required: false })
  updatedAt: string;

  @Prop({ required: true })
  type: string;
}

export const WeightSchema = SchemaFactory.createForClass(Weight);
