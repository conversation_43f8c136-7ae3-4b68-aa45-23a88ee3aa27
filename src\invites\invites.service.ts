import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { CreateInviteDto } from "./dto/create-invite.dto";
import { UpdateInviteDto } from "./dto/update-invite.dto";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { Invite } from "./schemas/invite.schema";
import { CoachService } from "src/coach/coach.service";
import { MailerService } from "@nestjs-modules/mailer";

@Injectable()
export class InvitesService {
  constructor(
    @InjectModel(Invite.name) private invitesModal: Model<Invite>,
    private readonly coachService: CoachService,
    private readonly mailerService: MailerService
  ) {}
  async create(createInviteDto: CreateInviteDto, user: any) {
    const invite = await this.invitesModal.create({
      ...createInviteDto,
      invitedBy: user.id,
    });
    if (!invite) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: `Error while creating invite`,
        },
        HttpStatus.BAD_REQUEST
      );
    } else {
      //get coach referral code
      const coach = await this.coachService.findCoach({ _id: user.id });
      if (!coach) {
        throw new HttpException(
          {
            status: HttpStatus.BAD_REQUEST,
            message: `no coach found with id ${user.id}`,
          },
          HttpStatus.BAD_REQUEST
        );
      }
      if (!coach.referralCode) {
        throw new HttpException(
          {
            status: HttpStatus.BAD_REQUEST,
            message: `coach have no referral code ${user.id}`,
          },
          HttpStatus.BAD_REQUEST
        );
      } else {
        // send email to client
        await this.mailerService.sendMail({
          to: createInviteDto.clientEmail,
          subject: "Coach Invitation",
          text: `You have received an invitation from Plate mate coach. Please sign up the in the application using the coach's referral code ${coach.referralCode}.`,
        });
        return {
          success: true,
          message: `Invitation sent to ${createInviteDto.clientEmail}`,
        };
      }
    }
  }

  findAll() {
    return `This action returns all invites`;
  }

  findOne(id: number) {
    return `This action returns a #${id} invite`;
  }

  update(id: number, updateInviteDto: UpdateInviteDto) {
    return `This action updates a #${id} invite`;
  }

  remove(id: number) {
    return `This action removes a #${id} invite`;
  }
}
