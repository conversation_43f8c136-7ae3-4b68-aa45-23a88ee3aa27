import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, SchemaFactory } from "@nestjs/mongoose";
import * as mongoose from "mongoose";
import { IExercise } from "../interface/exercises.interface";

@Schema({ timestamps: true })
export class Exercises extends mongoose.Document implements IExercise {
  @Prop({ required: true })
  title: string;

  @Prop({ required: true, })
  category: "CARDIO" | "STRENGTH" | "LIGHT";

  @Prop({ required: false })
  duration: number;

  @Prop({ required: true })
  calories: number;

  @Prop({ required: false })
  sets: number;

  @Prop({ required: false })
  reps: number;
}

export const ExercisesSchema = SchemaFactory.createForClass(Exercises);