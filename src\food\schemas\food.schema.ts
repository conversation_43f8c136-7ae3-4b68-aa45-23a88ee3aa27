import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import mongoose from "mongoose";
import { Client } from "src/client/schemas/client.schema";

@Schema({ timestamps: false })
export class Food {
  @Prop({ required: true })
  name: string;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: "Client",
    required: true,
  })
  client: mongoose.Types.ObjectId | Client;

  @Prop({ required: false })
  quantity: number;

  @Prop({ required: true })
  calories: number;

  @Prop({ required: true })
  proteins: number;

  @Prop({ required: true })
  carbs: number;

  @Prop({ required: true })
  fats: number;

  @Prop({ required: false, default: false })
  deleted: boolean;
  @Prop({ required: false })
  createdAt: string;

  @Prop({ required: false })
  updatedAt: string;

  @Prop({ required: false, default: false })
  addedByAdmin: boolean;
}

export const FoodSchema = SchemaFactory.createForClass(Food);
