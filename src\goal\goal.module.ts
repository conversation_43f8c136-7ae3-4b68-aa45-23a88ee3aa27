import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { GoalService } from "./goal.service";
import { GoalController } from "./goal.controller";
import { MongooseModule } from "@nestjs/mongoose";
import { Goal, GoalSchema } from "./schemas/goal.schema";
import { UsersModule } from "../users/users.module";
import { NotificationsModule } from "src/notifications/notifications.module";
import { NotificationsService } from "src/notifications/notifications.service";

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Goal.name, schema: GoalSchema }]),
    UsersModule,
    NotificationsModule,
  ],
  controllers: [GoalController],
  providers: [GoalService],
})
export class GoalModule { }
