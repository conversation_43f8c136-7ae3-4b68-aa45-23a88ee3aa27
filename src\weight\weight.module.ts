import { Modu<PERSON> } from "@nestjs/common";
import { WeightService } from "./weight.service";
import { WeightController } from "./weight.controller";
import { MongooseModule } from "@nestjs/mongoose";
import { Weight, WeightSchema } from "./schemas/weight.schema";
import { UploadImageModule } from "src/upload-image/upload-image.module";
import { ClientModule } from "src/client/client.module";

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Weight.name, schema: WeightSchema }]),
    UploadImageModule,
    ClientModule,
  ],
  controllers: [WeightController],
  providers: [WeightService],
})
export class WeightModule {}
