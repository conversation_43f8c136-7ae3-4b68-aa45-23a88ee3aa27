import { Injectable } from "@nestjs/common";
import { CreateGoalDto } from "./dto/create-goal.dto";
import { UpdateGoalDto } from "./dto/update-goal.dto";
import { InjectModel } from "@nestjs/mongoose";
import { Goal } from "./schemas/goal.schema";
import { Model } from "mongoose";
import { UsersService } from "src/users/users.service";
import { NotificationsService } from "src/notifications/notifications.service";

@Injectable()
export class GoalService {
  constructor(
    @InjectModel(Goal.name) private goalModel: Model<Goal>,
    private usersServices: UsersService,
    private notificationsService: NotificationsService
  ) { }
  async create(createGoalDto: CreateGoalDto, user: any) {
    let goal = await this.goalModel.create({
      ...createGoalDto,
      coach: user.id,
      createdAt: new Date().valueOf(),
      updatedAt: new Date().valueOf(),
    });

    let client = await this.usersServices.findOne({ _id: createGoalDto.client });

    if (client && client.fcmToken) {
      this.notificationsService.create({ title: "New Goal", body: "New goal has been assigned to you.", fcmToken: client.fcmToken, user: createGoalDto.client });
    }

    return goal;
  }

  findAll() {
    return `This action returns all goal`;
  }

  findOne(id: number) {
    return `This action returns a #${id} goal`;
  }

  update(id: string, updateGoalDto: UpdateGoalDto) {
    return this.goalModel.updateOne({ _id: id }, updateGoalDto);
  }

  remove(id: number) {
    return `This action removes a #${id} goal`;
  }
}
