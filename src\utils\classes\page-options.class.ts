import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "class-validator";
import { Transform, Type } from "class-transformer";

import { FieldsOrder } from "../enums";
import { ApiProperty } from "@nestjs/swagger";

// Reference: https://pietrzakadrian.medium.com/how-to-create-pagination-in-nest-js-with-typeorm-swagger-496b97fdd000
export class PageOptionsDto {
  @ApiProperty({ required: false })
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(500)
  @IsOptional()
  limit?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  skip?: number;

  @ApiProperty({ required: false, example: "createdAt " })
  @Type(() => String)
  @IsOptional()
  sortField?: string;

  @ApiProperty({ required: false, enum: FieldsOrder })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  @IsEnum(FieldsOrder)
  order?: number;
}
