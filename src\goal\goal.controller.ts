import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Req,
} from "@nestjs/common";
import { GoalService } from "./goal.service";
import { CreateGoalDto } from "./dto/create-goal.dto";
import { UpdateGoalDto } from "./dto/update-goal.dto";

@Controller("goal")
export class GoalController {
  constructor(private readonly goalService: GoalService) {}

  @Post()
  create(@Body() createGoalDto: CreateGoalDto, @Req() req) {
    return this.goalService.create(createGoalDto, req.user);
  }

  @Get()
  findAll() {
    return this.goalService.findAll();
  }

  @Get(":id")
  findOne(@Param("id") id: string) {
    return this.goalService.findOne(+id);
  }

  @Patch(":id")
  update(@Param("id") id: string, @Body() updateGoalDto: UpdateGoalDto) {
    return this.goalService.update(id, updateGoalDto);
  }

  @Delete(":id")
  remove(@Param("id") id: string) {
    return this.goalService.remove(+id);
  }
}
