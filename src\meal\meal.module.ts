import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MealService } from "./meal.service";
import { MealController } from "./meal.controller";
import { MongooseModule } from "@nestjs/mongoose";
import { Meal, MealSchema } from "./schemas/meal.schema";
import { FoodModule } from "src/food/food.module";
import { ReviewModule } from "src/review/review.module";
import { UploadImageModule } from "src/upload-image/upload-image.module";

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Meal.name, schema: MealSchema }]),
    FoodModule,
    ReviewModule,
    UploadImageModule,
  ],
  controllers: [MealController],
  providers: [MealService],
})
export class MealModule {}
