import { Schema, <PERSON>p, SchemaFactory } from "@nestjs/mongoose";
import * as mongoose from "mongoose";
import { ICoach } from "../interfaces/coach.interface";

@Schema({ timestamps: true })
export class Coach extends mongoose.Document implements ICoach {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true, unique: true, index: true })
  email: string;

  @Prop({ required: true })
  password: string;

  @Prop({ required: false })
  dob: string;

  @Prop({ required: false, default: "" })
  experience: string;

  @Prop({ required: false, default: "" })
  about: string;

  @Prop({ required: false, default: "" })
  photo: string;

  @Prop({ required: false, default: "" })
  code: string;

  @Prop({ required: false, default: 0 })
  verificationCodeExpiresAt: number;

  @Prop({ required: false, default: "" })
  referralCode: string;

  @Prop({ required: true, default: "" })
  userType: string;
}

export const CoachSchema = SchemaFactory.createForClass(Coach);
