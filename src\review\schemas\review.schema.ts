import { <PERSON>p, Schema, SchemaFactory } from "@nestjs/mongoose";
import mongoose from "mongoose";
import { Client } from "src/client/schemas/client.schema";
import { Coach } from "src/coach/schemas/coach.schema";
import { Food } from "src/food/schemas/food.schema";
import { Meal } from "src/meal/schemas/meal.schema";

@Schema({ timestamps: false })
export class Review {
  @Prop({ required: true })
  text: string;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: "Coach", required: true })
  coach: mongoose.Types.ObjectId | Coach;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: "Client",
    required: true,
  })
  client: mongoose.Types.ObjectId | Client;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: "Meal",
    required: true,
  })
  meal: mongoose.Types.ObjectId | Meal;

  @Prop({ required: false })
  createdAt: string;

  @Prop({ required: false })
  updatedAt: string;
}
export const ReviewSchema = SchemaFactory.createForClass(Review);
