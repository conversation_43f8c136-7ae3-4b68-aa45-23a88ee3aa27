import { ExceptionFilter, Catch, ArgumentsHost, HttpException, HttpStatus } from '@nestjs/common';
import { Request, Response } from 'express';

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const status =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;

    let errorMessage = 'Internal server error';

    if (exception instanceof HttpException) {
      errorMessage = this.getErrorMessage(exception);
    } else if (exception instanceof Error) {
      errorMessage = exception.message;
    }

    // Handle duplicate key errors
    if (exception instanceof Error && exception.message.includes('E11000 duplicate key error')) {
      errorMessage = 'A duplicate entry already exists.';
    }

    console.log(errorMessage)
    response.status(status).json({
      statusCode: status,
      message: errorMessage,
      timestamp: new Date().toISOString(),
      path: request.url,
    });
  }

  private getErrorMessage(exception: HttpException): any {
    const response = exception.getResponse();
    if (typeof response === 'string') {
      return response;
    } else if (typeof response === 'object' && 'message' in response) {
      return response['message'];
    }
    return 'Internal server error';
  }
}
