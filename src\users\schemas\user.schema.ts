import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, SchemaFactory } from "@nestjs/mongoose";
import * as mongoose from "mongoose";
import { IUser } from "../interfaces/user.interface";

@Schema({ timestamps: true })
export class User extends mongoose.Document implements IUser {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true, unique: true })
  email: string;

  @Prop({ required: true })
  userType: string;

  @Prop({ required: false })
  userId: string;

  @Prop({ required: false })
  password: string;

  @Prop({ required: false })
  fcmToken?: string;
}

export const UserSchema = SchemaFactory.createForClass(User);
