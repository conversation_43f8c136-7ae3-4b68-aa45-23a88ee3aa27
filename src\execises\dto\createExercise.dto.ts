import { ApiProperty } from "@nestjs/swagger";
import {
  IsString,
  IsOptional,
  IsN<PERSON>ber,
  IsEnum,
} from "class-validator";

export enum ExerciseCategory {
  CARDIO = "CARDIO",
  STRENGTH = "STRENGTH",
  LIGHT = "LIGHT",
}

export class CreateExerciseDTO {
  @ApiProperty({ example: "jon wick" })
  @IsString()
  title: string;

  @ApiProperty({ example: "CARDIO", enum: ExerciseCategory })
  @IsEnum(ExerciseCategory)
  category: ExerciseCategory;

  @ApiProperty({ example: 180 })
  @IsOptional()
  @IsNumber()
  duration?: number; // in seconds

  @ApiProperty({ example: 360 })
  @IsNumber()
  calories: number;

  @ApiProperty({ example: 5 })
  @IsOptional()
  @IsNumber()
  sets?: number;

  @ApiProperty({ example: 16 })
  @IsOptional()
  @IsNumber()
  reps?: number;
}
