import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Exercises } from './schema/exercises.schema';
import { Model } from 'mongoose';
import { CreateExerciseDTO } from './dto/createExercise.dto';
import { UpdateExerciseDTO } from './dto/updateExercise.dto';

@Injectable()
export class ExercisesService {
  constructor(@InjectModel(Exercises.name) private exercisesModel: Model<Exercises>,
  ) { }

  // * CREATE EXERCISE
  async create(body: CreateExerciseDTO) {
    return await this.exercisesModel.create(body);
  }

  // * FIND ALL EXERCISES
  async findAll() {
    return await this.exercisesModel.find({});
  }

  // * FIND EXERCISE BY ID
  async findById(id: string) {
    return await this.exercisesModel.findById(id);
  }

  // * UPDATE EXERCISE BY ID
  async updateById(id: string, payload: UpdateExerciseDTO) {
    return await this.exercisesModel.findById(id, payload, { new: true });
  }

  // * DELETE EXERCISE BY ID
  async deleteById(id: string) {
    return await this.exercisesModel.findByIdAndDelete(id);
  }
}
