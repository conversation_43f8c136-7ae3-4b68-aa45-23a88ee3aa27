// import { <PERSON>N<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Optional, IsString } from 'class-validator';
// import { Type } from 'class-transformer';
// import { FieldsOrder } from '../enums';
// // Reference: https://wanago.io/2021/09/13/api-nestjs-pagination-mongodb-mongoose/
// export class SortParams {
//   @Type(() => String)
//   @IsString()
//   @IsOptional()
//   sortField?: string;

//   @Type(() => Number)
//   @IsOptional()
//   @IsNumber()
//   @IsEnum(FieldsOrder)
//   order?: number;
// }
