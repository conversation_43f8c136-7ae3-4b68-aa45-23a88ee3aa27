import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseInterceptors,
  UploadedFile,
  ParseFilePipe,
  Req,
  Query,
} from "@nestjs/common";
import { DocumentService } from "./document.service";
import { CreateDocumentDto } from "./dto/create-document.dto";
import { UpdateDocumentDto } from "./dto/update-document.dto";
import { FileInterceptor } from "@nestjs/platform-express";

@Controller("document")
export class DocumentController {
  constructor(private readonly documentService: DocumentService) {}

  @Post()
  create(@Body() createDocumentDto: CreateDocumentDto, @Req() req) {
    return this.documentService.create(createDocumentDto, req.user);
  }

  @Post("/upload-doc")
  @UseInterceptors(FileInterceptor("file"))
  uploadFile(
    @UploadedFile(
      new ParseFilePipe({
        // validators: [new MaxFileSizeValidator({ maxSize: 2000 })],
      })
    )
    file: Express.Multer.File,
    @Req() req
  ) {
    return this.documentService.uploadFile(file, req.user);
  }

  @Get()
  findAll(@Req() req, @Query("searchKey") searchkey: string) {
    return this.documentService.findAll(req.user, searchkey);
  }

  @Get(":id")
  findOne(@Param("id") id: string) {
    return this.documentService.findOne(id);
  }

  @Get("/coach/:id")
  getCoachDocs(@Param("id") id: string) {
    return this.documentService.getCoachDocs(id);
  }

  @Patch(":id")
  update(
    @Param("id") id: string,
    @Body() updateDocumentDto: UpdateDocumentDto
  ) {
    return this.documentService.update({ _id: id }, updateDocumentDto);
  }

  @Delete(":id")
  removeDoc(@Param("id") id: string) {
    return this.documentService.removeDoc(id);
  }
}
