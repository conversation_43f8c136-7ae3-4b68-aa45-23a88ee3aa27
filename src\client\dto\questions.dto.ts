// export class CreateUserDto {}
import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsN<PERSON>ber, IsArray } from "class-validator";

export class QuestionDto {
  @ApiProperty({ example: "age?" })
  @IsString()
  prompt: string;

  @ApiProperty({ example: "[2]" })
  @IsArray()
  value: string[];

  @ApiProperty({ example: 1 })
  @IsNumber()
  step: number;

  @ApiProperty({ example: "choose-one" })
  @IsString()
  questionType: string;
}
