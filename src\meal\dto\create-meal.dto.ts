import { ApiProperty } from "@nestjs/swagger";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, IsOptional, IsString } from "class-validator";
import { Types } from "mongoose";

export class CreateMealDto {
  @ApiProperty({ example: "test review" })
  @IsString()
  mealTitle: string;

  @ApiProperty({ example: [{ foodId: "1234", serving: 1 }] })
  @IsArray()
  mealFoods: JSON;
}
