import {
  HttpException,
  HttpStatus,
  Injectable,
  UnauthorizedException,
} from "@nestjs/common";
const crypto = require("crypto");
import * as bcrypt from "bcrypt";
import { JwtService } from "@nestjs/jwt";
import { SignUpDto } from "./dto/signup.dto";
import { UsersService } from "../users/users.service";
import { ForgotPasswordDto } from "./dto/forgot-password.dto";
import { ResetPasswordDto } from "./dto/reset-password-otp.dto";
import { VerifyOptDto } from "./dto/verify-otp.dto";
import { generateHash, isExpire, otpgenerator } from "src/utils/comman";

import { MailerService } from "@nestjs-modules/mailer";
import { UpdateProfileDto } from "./dto/update-profile.dto";
import { ClientService } from "src/client/client.service";
import { CoachService } from "src/coach/coach.service";
import { ChangePasswordDto } from "src/client/dto/change-password.dto";

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    private readonly mailerService: MailerService,
    private readonly clientService: ClientService,
    private readonly coachService: CoachService
  ) { }

  async signIn(email: string, password: string) {
    const user = await this.usersService.findOne({ email: email });

    if (!user) {
      throw new UnauthorizedException();
    }
    if (user.userType === "coach") {
      return this.coachService.signIn(email, password);
    }
    if (user.userType === "client") {
      return this.clientService.signIn(email, password);
    }
    if (user.userType === "admin") {
      return this.usersService.signInAdmin(email, password);
    }
  }

  // async signUp (createAuthDto: SignUpDto) {
  //   const userExist = await this.usersService.findOne({
  //     email: createAuthDto.email
  //   })
  //   if (userExist) {
  //     throw new HttpException(
  //       {
  //         status: HttpStatus.BAD_REQUEST,
  //         message: 'Email Already exist'
  //       },
  //       HttpStatus.BAD_REQUEST
  //     )
  //   }
  //   const user = await this.usersService.create(createAuthDto)
  //   const payload = { name: user.name, email: user.email, id: user.id }
  //   return {
  //     access_token: await this.jwtService.signAsync(payload),
  //     name: user.name,
  //     email: user.email,
  //     id: user.id
  //   }
  // }

  generateAndSaveCode() {
    // Generate a random number between 0 and 9999 (inclusive)
    let randomNumber = Math.floor(Math.random() * 10000);

    // Pad the number with leading zeros if necessary
    let randomCode = randomNumber.toString().padStart(4, "0");

    // update user with code and expiresAt
    const currentTimestamp = new Date().valueOf();
    const fiveMinutesInMilliseconds = 5 * 60 * 1000; // 5 minutes in milliseconds
    const timestampAfterFiveMinutes =
      currentTimestamp + fiveMinutesInMilliseconds;

    return { randomCode, timestampAfterFiveMinutes };
  }

  async forgotPassword(forgotPasswordDto: ForgotPasswordDto) {
    const user = await this.usersService.findOne({
      email: forgotPasswordDto.email,
    });
    if (!user) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: "Invalid Email",
        },
        HttpStatus.BAD_REQUEST
      );
    }

    if (user.userType === "client") {
      return this.clientService.forgotPassword(forgotPasswordDto);
    }
    if (user.userType === "coach") {
      return this.coachService.forgotPassword(forgotPasswordDto);
    }

    // let { randomCode, timestampAfterFiveMinutes } = this.generateAndSaveCode();

    // const updatedUser = await this.usersService.update(
    //   { email: forgotPasswordDto.email },
    //   {
    //     code: randomCode,
    //     verificationCodeExpiresAt: timestampAfterFiveMinutes,
    //   }
    // );
    // if (!updatedUser) {
    //   throw new HttpException(
    //     {
    //       status: HttpStatus.BAD_REQUEST,
    //       message: "User not updated",
    //     },
    //     HttpStatus.BAD_REQUEST
    //   );
    // } else {
    //   await this.mailerService.sendMail({
    //     to: forgotPasswordDto.email,
    //     subject: "Reset Password OTP",
    //     text: `Your OTP for resetting password is ${randomCode}. This OTP will expire in 5 minutes.`,
    //   });
    //   return {
    //     success: true,
    //     message: `OTP sent to ${forgotPasswordDto.email}`,
    //   };
    // }
  }

  async verifyOtp(verifyOtpDto: VerifyOptDto): Promise<any> {
    const user = await this.usersService.findOne({ email: verifyOtpDto.email });
    if (!user) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: "Email does not exist",
        },
        HttpStatus.BAD_REQUEST
      );
    }
    if (user.userType === "client") {
      return this.clientService.verifyOtp(verifyOtpDto);
    }
    if (user.userType === "coach") {
      return this.coachService.verifyOtp(verifyOtpDto);
    }

    // const { code, verificationCodeExpiresAt } = user;
    // if (
    //   !code ||
    //   code !== verifyOtpDto.otp ||
    //   isExpire(verificationCodeExpiresAt)
    // ) {
    //   throw new HttpException(
    //     {
    //       status: HttpStatus.BAD_REQUEST,
    //       message: "Invalid OTP",
    //     },
    //     HttpStatus.BAD_REQUEST
    //   );
    // }

    // return { success: true, message: "otp verified successfully" };
  }

  async resetPassword(dto: ResetPasswordDto): Promise<any> {
    const user = await this.usersService.findOne({
      email: dto.email,
    });

    if (!user) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: `no user found with email ${dto.email}`,
        },
        HttpStatus.BAD_REQUEST
      );
    }

    if (user.userType === "client") {
      return this.clientService.resetPassword(dto);
    }
    if (user.userType === "coach") {
      return this.coachService.resetPassword(dto);
    }

    // if (
    //   !user.code ||
    //   dto.otp !== user.code ||
    //   isExpire(user.verificationCodeExpiresAt)
    // ) {
    //   throw new HttpException(
    //     {
    //       status: HttpStatus.BAD_REQUEST,
    //       message: `Invalid OTP code`,
    //     },
    //     HttpStatus.BAD_REQUEST
    //   );
    // }

    // // Reset password
    // const hashPassword = await bcrypt.hash(dto.password, 10);
    // await this.usersService.update(
    //   { email: dto.email },
    //   {
    //     password: hashPassword,
    //   }
    // );

    // return { success: true, message: "password updated successfully" };
  }

  async getProfile(email: string): Promise<any> {
    return await this.usersService.findOne({ email: email });
  }

  async updateUserPass(
    user: any,
    changePasswordDto: ChangePasswordDto
  ): Promise<any> {
    let userData;
    if (user.userType === "client") {
      userData = await this.clientService.findOne({ email: user.email });
    }
    if (user.userType === "coach") {
      userData = await this.coachService.findCoach({ email: user.email });
    }
    if (!userData) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: `user not found`,
        },
        HttpStatus.BAD_REQUEST
      );
    }

    if (userData.userType === "client") {
      return this.clientService.changePassword(changePasswordDto, userData);
    }
    if (userData.userType === "coach") {
      return this.coachService.changePassword(changePasswordDto, userData);
    }
  }

  async updateProfile(
    email: string,
    updateProfileDto: UpdateProfileDto
  ): Promise<any> {
    console.log("update ", updateProfileDto);
    return await this.usersService.update({ email: email }, updateProfileDto);
  }

  // async deleteAccount(email: string): Promise<any> {
  //   console.log("deleting account", email);
  //   return await this.usersService.delete({ email: email });
  // }
}
