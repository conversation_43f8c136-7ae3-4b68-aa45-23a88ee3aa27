import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { CreateDocumentDto } from "./dto/create-document.dto";
import { UpdateDocumentDto } from "./dto/update-document.dto";
import { InjectModel } from "@nestjs/mongoose";
import { Document } from "./schemas/document.schema";
import { Model } from "mongoose";
import { UploadImageService } from "src/upload-image/upload-image.service";

@Injectable()
export class DocumentService {
  constructor(
    @InjectModel(Document.name) private docModel: Model<Document>,
    private readonly uploadImageService: UploadImageService
  ) {}
  async create(createDocumentDto: CreateDocumentDto, user: any) {
    // upload doc to aws bucket

    return await this.docModel.create({ ...createDocumentDto, user: user.id });
  }

  async uploadFile(file: any, user: any) {
    const imageName = `${new Date().valueOf()}-${file.originalname}`;

    const imageData = await this.uploadImageService.uploadFile(
      file,
      user,
      imageName
    );
    return imageData;
  }

  findAll(user: any, searchKey: string) {
    // get all docs
    let query = {
      user: user.id,
    };
    if (searchKey) {
      query["$or"] = [
        { title: { $regex: searchKey, $options: "i" } }, // Case insensitive search in name
      ];
    }
    return this.docModel.find(query);
  }

  async findOne(id: string) {
    let doc = await this.docModel.findOne({ _id: id });
    if (!doc) {
      throw new HttpException(
        {
          status: HttpStatus.NOT_FOUND,
          message: `No doc with id ${id}`,
        },
        HttpStatus.NOT_FOUND
      );
    }
    // get image url from aws
    const image = await this.uploadImageService.getObjectSignedUrl(doc.docUrl);
    let docResult = doc.toObject();

    return { ...docResult, imageUrl: image };
  }

  async update(keyValue: any, updateUserDto: any): Promise<any> {
    const updatedUser = await this.docModel.findOneAndUpdate(
      keyValue,
      { ...updateUserDto },
      {
        new: true,
      }
    );

    return updatedUser;
  }

  async removeDoc(id: string) {
    // get document to get image name to delete from aws
    const doc = await this.docModel.findOne({ _id: id });
    if (!doc) {
      throw new HttpException(
        {
          status: HttpStatus.NOT_FOUND,
          message: `No doc with id ${id}`,
        },
        HttpStatus.NOT_FOUND
      );
    }
    // delete image from aws bucket
    await this.uploadImageService.deleteFile(doc.docUrl);
    // delete document
    const deleted = await this.docModel.findOneAndDelete({ _id: id });
    return deleted;
  }
  async getCoachDocs(id: string) {
    const docs = await this.docModel.find({ user: id });
    let documnets = [];
    if (docs.length) {
      for (const item of docs) {
        let doc = item.toObject();
        const url = await this.uploadImageService.getObjectSignedUrl(
          doc.docUrl
        );
        documnets.push({ ...doc, docUrl: url });
      }
    }
    return documnets;
  }
}
