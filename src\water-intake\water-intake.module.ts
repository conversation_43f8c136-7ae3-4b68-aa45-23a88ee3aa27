import { Modu<PERSON> } from "@nestjs/common";
import { WaterIntakeService } from "./water-intake.service";
import { WaterIntakeController } from "./water-intake.controller";
import { MongooseModule } from "@nestjs/mongoose";
import { WaterIntake, WaterIntakeSchema } from "./schemas/water-intake.schema";

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: WaterIntake.name, schema: WaterIntakeSchema },
    ]),
  ],
  controllers: [WaterIntakeController],
  providers: [WaterIntakeService],
})
export class WaterIntakeModule {}
