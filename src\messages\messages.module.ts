import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MessagesService } from "./messages.service";
import { Message, MessageSchema } from "./schemas/message.schema";
import { MongooseModule } from "@nestjs/mongoose";
import { MessageController } from "./message.controller";

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Message.name, schema: MessageSchema }]),
  ],
  controllers: [MessageController],
  providers: [MessagesService],
  exports: [MessagesService],
})
export class MessagesModule {}
