import { ApiProperty } from "@nestjs/swagger";
import { IsString } from "class-validator";

export class CreateReviewDto {
  @ApiProperty({ example: "test review" })
  @IsString()
  text: string;

  @ApiProperty({ example: "12345" })
  @IsString()
  coach: string;

  @ApiProperty({ example: "12345" })
  @IsString()
  client: string;

  @ApiProperty({ example: "breakfast" })
  @IsString()
  meal: string;
}
