import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Req,
  Query,
} from "@nestjs/common";
import { WaterIntakeService } from "./water-intake.service";
import { CreateWaterIntakeDto } from "./dto/create-water-intake.dto";
import { UpdateWaterIntakeDto } from "./dto/update-water-intake.dto";

@Controller("water-intake")
export class WaterIntakeController {
  constructor(private readonly waterIntakeService: WaterIntakeService) {}

  @Post()
  create(@Body() createWaterIntakeDto: CreateWaterIntakeDto, @Req() req) {
    return this.waterIntakeService.create(createWaterIntakeDto, req.user);
  }

  @Get("client/:id")
  findAll(@Param("id") id: string) {
    return this.waterIntakeService.findAll(id);
  }

  @Get(":id")
  findOne(@Param("id") id: string) {
    return this.waterIntakeService.findOne(id);
  }

  @Patch(":id")
  update(
    @Param("id") id: string,
    @Body() updateWaterIntakeDto: UpdateWaterIntakeDto
  ) {
    return this.waterIntakeService.update(id, updateWaterIntakeDto);
  }

  @Delete(":id")
  remove(@Param("id") id: string) {
    return this.waterIntakeService.remove(id);
  }

  @Get("get/date")
  findOneByDate(@Req() req, @Query("date") date: string) {
    return this.waterIntakeService.findOneByDate(req.user, date);
  }
}
