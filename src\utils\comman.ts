import * as bcrypt from "bcrypt";
const crypto = require("crypto");
export async function generateHash() {
  const plainTextToken = "myrandomstring123";

  // Generate a salt to use for the hash
  const saltRounds = 10;
  const salt = await bcrypt.genSalt(saltRounds);

  // Hash the token using the salt
  const hash = await bcrypt.hash(plainTextToken, salt);

  return hash;
}

export function otpgenerator() {
  return Math.floor(Math.random() * (9999 - 1000) + 1000);
}

export function isExpire(expireDate: number) {
  const currentDate = new Date().valueOf();
  const result = expireDate < currentDate;
  return result;
}

export function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 6371; // Radius of the earth in kilometers
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);

  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(lat1)) *
      Math.cos(deg2rad(lat2)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c; // Distance in kilometers
  return distance;
}

function deg2rad(deg) {
  return deg * (Math.PI / 180);
}

export const comparePassword = (
  password: string,
  hashedPassword: string
): Promise<boolean> => {
  return bcrypt.compare(password, hashedPassword);
};

export const generateAndSaveCode = () => {
  // Generate a random number between 0 and 9999 (inclusive)
  let randomNumber = Math.floor(Math.random() * 10000);

  // Pad the number with leading zeros if necessary
  let randomCode = randomNumber.toString().padStart(4, "0");

  // update user with code and expiresAt
  const currentTimestamp = new Date().valueOf();
  const fiveMinutesInMilliseconds = 5 * 60 * 1000; // 5 minutes in milliseconds
  const timestampAfterFiveMinutes =
    currentTimestamp + fiveMinutesInMilliseconds;

  return { randomCode, timestampAfterFiveMinutes };
};

export const generateFileName = (bytes = 32) =>
  crypto.randomBytes(bytes).toString("hex");
