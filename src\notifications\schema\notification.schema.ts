import * as mongoose from 'mongoose';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { INotification } from '../interface/notifications.interface';
import { User } from '../../users/schemas/user.schema';

@Schema({ timestamps: true })
export class Notifications extends mongoose.Document implements INotification {
  @Prop({ required: true })
  title: string;

  @Prop({ required: true, type: String })
  body: string;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: User.name, required: true })
  user: string;

  @Prop({ default: false })
  isRead: boolean;
}
export const NotificationsSchema = SchemaFactory.createForClass(Notifications);
