import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, SchemaFactory } from "@nestjs/mongoose";
import mongoose from "mongoose";
import { <PERSON> } from "src/coach/schemas/coach.schema";
@Schema({ timestamps: true })
export class Document {
  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  description: string;

  @Prop({ required: true })
  docUrl: string;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: "Coach", required: true })
  // user: string
  user: mongoose.Types.ObjectId | Coach;
}

export const DocumentSchema = SchemaFactory.createForClass(Document);
