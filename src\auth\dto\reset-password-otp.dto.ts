// export class CreateUserDto {}
import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsString, IsEmail, IsNumber } from 'class-validator'

export class ResetPasswordDto {
  @ApiProperty({ example: 'pass@34s!2#$' })
  @IsString()
  password: string

  @ApiProperty({ example: '1234' })
  @IsString()
  otp: string

  @ApiProperty({ example: '<EMAIL>' })
  @IsString()
  email: string
}
