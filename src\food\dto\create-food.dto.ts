import { ApiProperty } from "@nestjs/swagger";
import { IsBoolean, IsN<PERSON>ber, IsOptional, IsString } from "class-validator";

export class CreateFoodDto {
  @ApiProperty({ example: "test review" })
  @IsString()
  name: string;

  @ApiProperty({ example: 1 })
  @IsOptional()
  @IsNumber()
  quantity: number;

  @ApiProperty({ example: 200 })
  @IsNumber()
  calories: number;

  @ApiProperty({ example: 20 })
  @IsNumber()
  proteins: number;

  @ApiProperty({ example: 20 })
  @IsNumber()
  carbs: number;

  @ApiProperty({ example: 20 })
  @IsNumber()
  fats: number;

  @ApiProperty({ example: true })
  @IsOptional()
  @IsBoolean()
  addedByAdmin?: boolean;
}
