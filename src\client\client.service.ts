import {
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import moment from '../utils/moment';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { SignUpDto } from './dto/signup.dto';
import { Client } from './schemas/client.schema';
import {
  comparePassword,
  generateAndSaveCode,
  isExpire,
  generateFileName,
} from 'src/utils/comman';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { MailerService } from '@nestjs-modules/mailer';
import { VerifyOtpDto } from './dto/verify-otp.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { IClient } from './interfaces/client.interface';
import { CoachService } from 'src/coach/coach.service';
import { UploadImageService } from 'src/upload-image/upload-image.service';
// import { RoomDocument } from "../chat/schemas/room.schema";
import { Goal } from "src/goal/schemas/goal.schema";
import { Meal } from "src/meal/schemas/meal.schema";
import { FoodService } from "src/food/food.service";
import { ReviewService } from "src/review/review.service";
import { Document } from "src/document/schemas/document.schema";
import { UsersService } from "src/users/users.service";
import { Chat } from "src/chats/schemas/chat.schema";
import { Coach } from "src/coach/schemas/coach.schema";
import { GetClientsDto } from "./dto/get-clients.dto";
import { ExercisesService } from "src/execises/exercises.service";
import { WorkoutService } from "src/workout/workout.service";

@Injectable()
export class ClientService {
  constructor(
    @InjectModel(Client.name) private clientModel: Model<Client>,
    // @InjectModel("Room") private readonly roomModel: Model<RoomDocument>,
    @InjectModel(Goal.name) private readonly goalModel: Model<Goal>,
    @InjectModel(Meal.name) private readonly mealModel: Model<Meal>,
    @InjectModel(Document.name) private readonly docModel: Model<Document>,
    @InjectModel(Chat.name) private readonly chatModal: Model<Chat>,

    private jwtService: JwtService,
    private readonly mailerService: MailerService,
    private readonly coachService: CoachService,
    private readonly uploadImageService: UploadImageService,
    private readonly foodService: FoodService,
    private readonly reviewService: ReviewService,
    private readonly userService: UsersService,
    private readonly workoutService: WorkoutService,
  ) { }
  async signUp(createAuthDto: SignUpDto) {
    const userExist = await this.clientModel.findOne({
      email: createAuthDto.email,
    });
    if (userExist) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: 'Email Already exist',
        },
        HttpStatus.BAD_REQUEST
      );
    }
    if (createAuthDto.referralCode) {
      // get coach from database with referalcode
      const coach = await this.coachService.findCoach({
        referralCode: createAuthDto.referralCode,
      });
      if (coach) {
        createAuthDto = { ...createAuthDto, coach: coach._id };
      }
    }
    const hashedPassword = await bcrypt.hash(createAuthDto.password, 10);
    const signupData = { ...createAuthDto, password: hashedPassword };
    const client = await this.clientModel.create({
      ...signupData,
      userType: 'client',
    });
    const payload = {
      name: client.name,
      email: client.email,
      id: client.id,
      coach: client.coach,
      userType: 'client',
    };
    // create entry in user table
    await this.userService.create({
      name: client.name,
      email: client.email,
      userType: client.userType,
      userId: client._id,
    });
    return {
      access_token: await this.jwtService.signAsync(payload),
      name: client.name,
      email: client.email,
      id: client.id,
      referralCode: client.referralCode,
      userType: client.userType,
    };
  }

  async signIn(email: string, password: string) {
    let user = await this.clientModel.findOne({ email });
    if (!user || !(await comparePassword(password, user.password))) {
      throw new UnauthorizedException();
    }
    const payload = {
      name: user.name,
      email: user.email,
      id: user['_id'],
      userType: user.userType,
      coach: user.coach,
    };
    let url = '';
    if (user.image) {
      url = await this.uploadImageService.getObjectSignedUrl(user.image);
      user.image = url;
    }

    const chats = await this.chatModal
      .find({ users: { $in: [user.id] } })
      .populate('lastMessage');
    user = user.toObject();
    user['chats'] = chats;
    return {
      access_token: await this.jwtService.signAsync(payload),
      ...user,
    };
  }

  async forgotPassword(forgotPasswordDto: ForgotPasswordDto) {
    const user = await this.clientModel.findOne({
      email: forgotPasswordDto.email,
    });
    if (!user) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: 'Invalid Email',
        },
        HttpStatus.BAD_REQUEST
      );
    }

    let { randomCode, timestampAfterFiveMinutes } = generateAndSaveCode();

    const updatedUser = await this.clientModel.updateOne(
      { email: forgotPasswordDto.email },
      {
        code: randomCode,
        verificationCodeExpiresAt: timestampAfterFiveMinutes,
      }
    );
    if (!updatedUser) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: 'User not updated',
        },
        HttpStatus.BAD_REQUEST
      );
    } else {
      await this.mailerService.sendMail({
        to: forgotPasswordDto.email,
        subject: 'Reset Password OTP',
        text: `Your OTP for resetting password is ${randomCode}. This OTP will expire in 5 minutes.`,
      });
      return {
        success: true,
        message: `OTP sent to ${forgotPasswordDto.email}`,
      };
    }
  }
  async verifyOtp(verifyOtpDto: VerifyOtpDto): Promise<any> {
    const user = await this.clientModel.findOne({ email: verifyOtpDto.email });
    if (!user) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: 'Email does not exist',
        },
        HttpStatus.BAD_REQUEST
      );
    }

    const { code, verificationCodeExpiresAt } = user;
    if (
      !code ||
      code !== verifyOtpDto.otp ||
      isExpire(verificationCodeExpiresAt)
    ) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: 'Invalid OTP',
        },
        HttpStatus.BAD_REQUEST
      );
    }

    return { success: true, message: 'otp verified successfully' };
  }
  async resetPassword(dto: ResetPasswordDto): Promise<any> {
    const user = await this.clientModel.findOne({
      email: dto.email,
    });

    if (!user) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: `no user found with email ${dto.email}`,
        },
        HttpStatus.BAD_REQUEST
      );
    }

    if (
      !user.code ||
      dto.otp !== user.code ||
      isExpire(user.verificationCodeExpiresAt)
    ) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: `Invalid OTP code`,
        },
        HttpStatus.BAD_REQUEST
      );
    }

    // Reset password
    const hashPassword = await bcrypt.hash(dto.password, 10);
    await this.clientModel.updateOne(
      { email: dto.email },
      {
        password: hashPassword,
      }
    );

    return { success: true, message: 'password updated successfully' };
  }
  async getProfile(email: string): Promise<any> {
    let user = await this.clientModel
      .findOne({ email: email })
      .populate('coach');
    let photos = [];
    if (user.photos.length) {
      for (const photo of user.photos) {
        const url = await this.uploadImageService.getObjectSignedUrl(
          photo.value
        );
        photos.push({ ...photo, url });
      }
      user.photos = photos;
    }
    if (user.image) {
      const url = await this.uploadImageService.getObjectSignedUrl(user.image);
      user['image'] = url;
    }
    const chats = await this.chatModal
      .find({ users: { $in: [user.id] } })
      .populate('lastMessage');
    user = user.toObject();
    user['chats'] = chats;
    return user;
  }

  async update(keyValue: any, updateUserDto: any): Promise<IClient> {
    if (updateUserDto.referralCode) {
      // fetch coach from ref code and add coach to client
      const coach = await this.coachService.findCoach({
        referralCode: updateUserDto.referralCode,
      });
      if (coach) {
        updateUserDto = { ...updateUserDto, coach: coach._id };
        // update clinets existing meals with coach id
        // Perform the update
        const res = await this.mealModel.updateMany(
          { client: keyValue._id },
          {
            $set: { coach: coach._id },
          }
        );
      }
    }
    const updatedUser = await this.clientModel.findOneAndUpdate(
      keyValue,
      { ...updateUserDto },
      {
        new: true,
      }
    );

    return updatedUser;
  }

  async changePassword(payload: any, user: any) {
    const { oldPassword, newPassword } = payload;
    const verifyPass = await comparePassword(oldPassword, user.password);
    if (!verifyPass) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: `Old password is incorrect`,
        },
        HttpStatus.BAD_REQUEST
      );
    }
    const hashPassword = await bcrypt.hash(newPassword, 10);

    return await this.update({ email: user.email }, { password: hashPassword });
  }

  async uploadPhotos(file: any, user: any) {
    const imageName = generateFileName();
    const imageData = await this.uploadImageService.uploadFile(
      file,
      user,
      imageName
    );
    return imageData;
  }
  async uploadFile(file: any, user: any) {
    const imageName = generateFileName();
    const imageData = await this.uploadImageService.uploadFile(
      file,
      user,
      imageName
    );
    await this.update({ _id: user.id }, { image: imageData.imageName });
    return { imageUrl: imageData.url };
  }

  // FIND ALL CLIENTS
  async findAll(filters: GetClientsDto) {
    const { pageNumber, pageSize, sort } = filters;
    const query = this.clientModel.find().select('-password');

    // TOTAL COUNT OF CLIENTS
    const count = await this.clientModel.countDocuments(query);

    // SORTING
    const sortOrder = sort === 'asc' ? 1 : -1;
    query.sort({ createdAt: sortOrder });

    // PAGINATION
    if (pageNumber && pageSize) {
      const skipDocs = (pageNumber - 1) * pageSize;
      query.skip(skipDocs).limit(pageSize);
    }
    const clients = await query.exec();

    return {
      count,
      pageNumber,
      pageSize,
      clients,
    };
  }

  async clientsByCoach(
    coachId: string,
    searchKey: string,
    sortKey: string,
    sortOrder: string
  ) {
    let query = {
      coach: coachId,
      archived: false,
      deleted: false,
    };

    if (searchKey) {
      query['$or'] = [
        { name: { $regex: searchKey, $options: 'i' } }, // Case insensitive search in name
        { email: { $regex: searchKey, $options: 'i' } }, // Case insensitive search in email
      ];
    }

    let sortOptions = {};
    if (sortKey === 'name') {
      sortOptions = { name: 1 }; // Use 1 for ascending
    }
    if (sortKey === 'createdAt') {
      sortOptions = { createdAt: sortOrder === 'asc' ? 1 : -1 }; // Use 1 for ascending, -1 for descending
    }
    const clients = await this.clientModel
      .find(query)
      .sort(sortOptions) // Apply sorting here
      .populate('coach');

    let data = [];
    if (clients.length) {
      // loop on clients for profile image
      for (let client of clients) {
        let url = client.image;
        if (client.image) {
          // get image url from aws bucket
          url = await this.uploadImageService.getObjectSignedUrl(client.image);
          client['image'] = url;
        }
        const chats = await this.chatModal
          .find({ users: { $in: [client._id] } })
          .populate('lastMessage');
        client = client.toObject();
        client['chats'] = chats;
        data.push(client);
      }
    }
    return { data, count: clients.length };
  }
  async deleteClient(clientId: string) {
    return this.clientModel.updateOne({ _id: clientId }, { deleted: true });
  }
  async archivedClients(coachId: string) {
    const clients = await this.clientModel.find({
      coach: coachId,
      archived: true,
      deleted: false,
    });
    let clinetsData = [];
    for (const client of clients) {
      if (client.image) {
        const url = await this.uploadImageService.getObjectSignedUrl(
          client.image
        );
        client['image'] = url;
      }
      clinetsData.push(client);
    }
    return { clients: clinetsData, count: clients.length };
  }

  async getById(clientId: string) {
    const clientData = await this.clientModel.findOne({ _id: clientId }).populate("coach");

    if (!clientData) {
      return new NotFoundException("Client not found");
    }

    const chats = await this.chatModal.find({ users: { $in: [clientId] } }).populate("lastMessage");
    let client = clientData.toObject();
    let coachData: any = client.coach;

    if (coachData?.photo) {
      const url = await this.uploadImageService.getObjectSignedUrl(
        coachData.photo
      );
      coachData['photo'] = url;
    }

    client["coach"] = coachData;
    client["chats"] = chats;
    let photos = [];

    if (client.photos.length) {
      for (const photo of client.photos) {
        const url = await this.uploadImageService.getObjectSignedUrl(
          photo.value
        );
        photos.push({ ...photo, url });
      }
      client.photos = photos;
    }

    if (client.image) {
      const url = await this.uploadImageService.getObjectSignedUrl(
        client.image
      );
      client['image'] = url;
    }

    const startOfDay = moment().startOf('day').valueOf(); // Start of day in milliseconds

    const endOfDay = moment().endOf('day').valueOf(); // End of day in milliseconds

    const goal = await this.goalModel.findOne({
      client: clientId,
      // createdAt: { $gte: startOfDay, $lte: endOfDay },
    }, {}, { sort: { 'createdAt': -1 } });

    const meals = await this.mealModel.find({
      client: clientId,
      createdAt: { $gte: startOfDay, $lte: endOfDay },
    });

    let mealData = [];
    let usedData = {
      usedCalories: 0,
      usedProtiens: 0,
      usedCarbs: 0,
      usedFats: 0,
    };

    if (meals.length) {
      for (const meal of meals) {
        let mealDoc = meal.toObject();
        let foods = [];
        if (mealDoc.mealFoods.length) {
          for (const food of mealDoc.mealFoods) {
            const foodData = await this.foodService.findOne(
              food.foodId.toString()
            );

            let foodDoc = foodData.toObject();
            usedData.usedCalories += foodDoc.calories;
            usedData.usedProtiens += foodDoc.proteins;
            usedData.usedCarbs += foodDoc.carbs;
            usedData.usedFats += foodDoc.fats;

            foods.push({ ...foodDoc, serving: food.serving });
          }
        }
        mealData.push({ ...mealDoc, mealFoods: foods });
      }
    }

    // COUNT BURNT CALORIES
    let workouts = await this.workoutService.findAll({ client: clientId, date: new Date().toString() });
    let exercises_done = workouts.flatMap(item => item.exercises || []); // Use `flatMap` to handle nested arrays
    const totalBurntCalories = exercises_done.reduce((sum, exercise: any) => {
      const calories = parseFloat(exercise?.calories || 0);
      return sum + calories;
    }, 0);

    return { client, usedData, goal, mealData, totalBurntCalories };
  }

  async getMealByDate(clientId: string, paramDate: string) {
    const startOfDay = moment(paramDate).startOf('day').valueOf(); // Start of day in milliseconds

    const endOfDay = moment(paramDate).endOf('day').valueOf(); // End of day in milliseconds

    const meals = await this.mealModel.find({
      client: clientId,
      createdAt: { $gte: startOfDay, $lte: endOfDay },
    });

    if (!meals.length) return [];
    let mealData = [];
    for (const meal of meals) {
      let mealDoc = meal.toObject();
      // find food by id
      let foods = [];
      if (mealDoc.mealFoods.length) {
        for (const food of mealDoc.mealFoods) {
          const foodData = await this.foodService.findOne(
            food.foodId.toString()
          );
          const review = await this.reviewService.findOne(
            String(mealDoc._id),
            food.foodId.toString()
          );
          let foodDoc = foodData.toObject();

          foods.push({ ...foodDoc, review, serving: food.serving });
        }
      }
      mealData.push({ ...mealDoc, mealFoods: foods });
    }
    return mealData;
  }

  async getGoalByDate(clientId: string, paramDate: string) {
    const startOfDay = moment(paramDate).startOf('day').valueOf(); // Start of day in milliseconds

    const endOfDay = moment(paramDate).endOf('day').valueOf(); // End of day in milliseconds

    const goal = await this.goalModel
      .find({
        client: clientId,
        createdAt: { $gte: startOfDay, $lte: endOfDay },
      })
      .populate('client')
      .populate('coach');

    return goal;
  }

  async getAllGoals(clientId: string) {
    const goals = await this.goalModel
      .find({
        client: clientId,
      })
      .populate('coach');

    return goals;
  }

  async clientDocs(clientId: string) {
    const docs = await this.docModel.find({
      user: clientId,
    });
    let documents = [];
    if (docs.length) {
      for (const doc of docs) {
        const url = await this.uploadImageService.getObjectSignedUrl(
          doc.docUrl
        );
        doc['docUrl'] = url;
        documents.push(doc);
      }
    }
    return documents;
  }

  async getClientCoach(clientId: string) {
    const data = await this.clientModel
      .findOne({ _id: clientId })
      .populate('coach');
    let coach: any = data.coach;
    if (coach?.photo) {
      const url = await this.uploadImageService.getObjectSignedUrl(coach.photo);
      coach.photo = url;
    }
    if (!coach) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: `no data found`,
        },
        HttpStatus.BAD_REQUEST
      );
    }

    return coach;
  }

  async findOne(keyValuepair: any): Promise<any> {
    return this.clientModel.findOne(keyValuepair).lean();
  }
  async findWeightData(clientId: string) {
    return this.clientModel.findOne(
      { _id: clientId },
      {
        _id: 0,
        weight: 1,
        photos: 1,
        client: 1,
        createdAt: 1,
        updatedAt: 1,
      }
    );
  }
}
