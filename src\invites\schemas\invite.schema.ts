import { <PERSON><PERSON><PERSON>, <PERSON>p, SchemaFactory } from "@nestjs/mongoose";
import * as mongoose from "mongoose";
import { User } from "src/users/schemas/user.schema";

@Schema({ timestamps: true })
export class Invite extends mongoose.Document {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true, unique: true, index: true })
  clientEmail: string;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: "User", required: false })
  invitedBy: mongoose.Types.ObjectId | User;
}

export const InviteSchema = SchemaFactory.createForClass(Invite);
