import { <PERSON>du<PERSON> } from "@nestjs/common";
import { ClientService } from "./client.service";
import { ClientController } from "./client.controller";
import { MongooseModule } from "@nestjs/mongoose";
import { Client, ClientSchema } from "./schemas/client.schema";
import { CoachModule } from "src/coach/coach.module";
import { UploadImageModule } from "src/upload-image/upload-image.module";
import { QuestionsModule } from "src/questions/questions.module";
// import { RoomSchema } from "src/chat/schemas/room.schema";
import { Goal, GoalSchema } from "src/goal/schemas/goal.schema";
import { Meal, MealSchema } from "src/meal/schemas/meal.schema";
import { FoodModule } from "src/food/food.module";
import { ReviewModule } from "src/review/review.module";
import { Document, DocumentSchema } from "src/document/schemas/document.schema";
import { UsersModule } from "src/users/users.module";
import { Chat, ChatSchema } from "src/chats/schemas/chat.schema";
import { WorkoutModule } from "src/workout/workout.module";

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Client.name, schema: ClientSchema },
      { name: Chat.name, schema: ChatSchema },
      { name: Goal.name, schema: GoalSchema },
      { name: Meal.name, schema: MealSchema },
      { name: Document.name, schema: DocumentSchema },
    ]),
    CoachModule,
    UploadImageModule,
    QuestionsModule,
    FoodModule,
    ReviewModule,
    UsersModule,
    WorkoutModule
  ],
  controllers: [ClientController],
  providers: [ClientService],
  exports: [ClientService],
})
export class ClientModule { }
