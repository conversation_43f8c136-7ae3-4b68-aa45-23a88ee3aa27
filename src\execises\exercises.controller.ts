import { Body, Controller, Delete, Get, Param, Post, Put } from '@nestjs/common';
import { ExercisesService } from './exercises.service';
import { CreateExerciseDTO } from './dto/createExercise.dto';
import { UpdateExerciseDTO } from './dto/updateExercise.dto';

@Controller('exercises')
export class ExercisesController {
  constructor(private readonly exercisesService: ExercisesService) { }

  // * POST -> /exercises
  @Post()
  async createExercise(
    @Body() body: CreateExerciseDTO
  ) {
    return await this.exercisesService.create(body);
  }

  // * POST -> /exercises
  @Get()
  async findAll() {
    return await this.exercisesService.findAll();
  }

  // * GET -> /exercises/:id
  @Get(":id")
  async findById(
    @Param("id") id: string
  ) {
    return await this.exercisesService.findById(id);
  }

  // * PUT -> /exercises/:id
  @Put(":id")
  async updatedById(
    @Param("id") id: string,
    @Body() body: UpdateExerciseDTO,
  ) {
    return await this.exercisesService.updateById(id, body);
  }

  // * DELETE -> /exercises/:id
  @Delete(":id")
  async deletedById(
    @Param("id") id: string,
  ) {
    return await this.exercisesService.deleteById(id);
  }
}
