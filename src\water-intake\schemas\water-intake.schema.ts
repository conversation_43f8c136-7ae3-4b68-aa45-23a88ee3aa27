import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import mongoose from "mongoose";
import { Client } from "src/client/schemas/client.schema";

@Schema({ timestamps: false })
export class WaterIntake {
  @Prop({ required: true })
  value: string;

  @Prop({ required: true })
  unit: string;
  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: "Client",
    required: true,
  })
  client: mongoose.Types.ObjectId | Client;

  @Prop({ required: false })
  createdAt: string;

  @Prop({ required: false })
  updatedAt: string;

  @Prop()
  notes?: string;
}

export const WaterIntakeSchema = SchemaFactory.createForClass(WaterIntake);
