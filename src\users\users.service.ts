import { Injectable, UnauthorizedException } from "@nestjs/common";
import * as bcrypt from "bcrypt";
import { Model } from "mongoose";
import { CreateUserDto } from "./dto/create-user.dto";
import { UpdateUserDto } from "./dto/update-user.dto";
import { InjectModel } from "@nestjs/mongoose";
import { User } from "./schemas/user.schema";
import { IUser } from "./interfaces/user.interface";
import { comparePassword } from "src/utils/comman";
import { JwtService } from "@nestjs/jwt";

@Injectable()
export class UsersService {
  constructor(@InjectModel(User.name) private userModel: Model<User>,
    private jwtService: JwtService,
  ) { }

  async create(createUserDto: CreateUserDto): Promise<any> {
    const createdUser = await this.userModel.create(createUserDto);

    return {
      name: createdUser.name,
      email: createdUser.email,
      id: createdUser._id,
    };
  }

  // ADMIN SIGN IN
  async signInAdmin(email: string, password: string) {
    const admin = (await this.userModel.findOne({ email })).toObject();

    // INVALID CREDENTIALS
    if (!admin || !(await comparePassword(password, admin.password))) {
      throw new UnauthorizedException();
    }

    const payload = {
      name: admin.name,
      email: admin.email,
      id: admin["_id"],
      userType: admin.userType,
      isAdmin: true
    };
    const { password: adminPassword, ...adminWithOutPassword } = admin;

    return {
      access_token: await this.jwtService.signAsync(payload),
      ...adminWithOutPassword
    };
  }

  async comparePassword(
    password: string,
    hashedPassword: string
  ): Promise<boolean> {
    return bcrypt.compare(password, hashedPassword);
  }

  async findAll(): Promise<IUser[]> {
    return await this.userModel.find().lean();
  }

  async getUsers(check: boolean): Promise<IUser[]> {
    return await this.userModel.find({ active: check }).lean();
  }

  async findOne(keyValuepair: any): Promise<IUser> {
    return await this.userModel.findOne(keyValuepair).lean();
  }

  // async delete(keyValuepair: any): Promise<IUser> {
  //   return await this.userModel.findOneAndDelete(keyValuepair);
  // }

  async update(keyValue: any, updateUserDto: any, user?: any): Promise<IUser> {
    const updatedUser = await this.userModel.findOneAndUpdate(
      keyValue,
      { ...updateUserDto },
      {
        new: true,
      }
    );

    return updatedUser;
  }
}
