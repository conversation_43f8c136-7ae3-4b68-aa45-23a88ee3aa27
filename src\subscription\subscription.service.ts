import { Injectable } from "@nestjs/common";
import { CreateSubscriptionDto } from "./dto/create-subscription.dto";
import { UpdateSubscriptionDto } from "./dto/update-subscription.dto";
import { Subscription } from "./schemas/subscription.schema";
import { Model } from "mongoose";
import { InjectModel } from "@nestjs/mongoose";

@Injectable()
export class SubscriptionService {
  constructor(
    @InjectModel(Subscription.name)
    private subscriptionModel: Model<Subscription>
  ) {}
  create(createSubscriptionDto: CreateSubscriptionDto) {
    return this.subscriptionModel.create(createSubscriptionDto);
  }

  findAll() {
    return this.subscriptionModel.find();
  }

  findOne(id: string) {
    return this.subscriptionModel.findOne({ _id: id });
  }

  async update(keyValue: any, updateUserDto: any): Promise<any> {
    const updatedUser = await this.subscriptionModel.findOneAndUpdate(
      keyValue,
      { ...updateUserDto },
      {
        new: true,
      }
    );

    return updatedUser;
  }

  remove(id: number) {
    return `This action removes a #${id} subscription`;
  }
}
