import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import moment from '../utils/moment';
import { CreateMealDto } from './dto/create-meal.dto';
import { UpdateMealDto } from './dto/update-meal.dto';
import { InjectModel } from '@nestjs/mongoose';
import { Meal } from './schemas/meal.schema';
import { Model, ObjectId } from 'mongoose';
import { FoodService } from 'src/food/food.service';
import { ReviewService } from 'src/review/review.service';
import { UploadImageService } from 'src/upload-image/upload-image.service';

@Injectable()
export class MealService {
  constructor(
    @InjectModel(Meal.name) private mealModel: Model<Meal>,
    private readonly foodService: FoodService,
    private readonly reviewService: ReviewService,
    private readonly uploadImageService: UploadImageService
  ) {}
  create(createMealDto: CreateMealDto, user: any) {
    return this.mealModel.create({
      ...createMealDto,
      client: user.id,
      coach: user.coach,
      createdAt: new Date().valueOf(),
      updatedAt: new Date().valueOf(),
    });
  }

  async findAll(user: any, id: string, date?: string) {
    const startOfDay = moment(date).startOf('day').valueOf(); // Start of day in milliseconds

    const endOfDay = moment(date).endOf('day').valueOf(); // End of day in milliseconds
    const meals = await this.mealModel
      .find({
        client: id,
        createdAt: { $gte: startOfDay, $lte: endOfDay },
      })
      .populate('client');

    if (!meals.length) return [];
    let mealData = [];
    for (const meal of meals) {
      let mealDoc = meal.toObject();
      // find food by id
      let foods = [];
      if (mealDoc.mealFoods.length) {
        for (const food of mealDoc.mealFoods) {
          const foodData = await this.foodService.findOne(
            food.foodId.toString()
          );
          let foodDoc = foodData.toObject();

          foods.push({ ...foodDoc, serving: food.serving });
        }
      }
      mealData.push({ ...mealDoc, mealFoods: foods });
    }
    return mealData;
  }

  async findOne(id: string) {
    const meal = await this.mealModel.findOne({ _id: id });
    if (!meal)
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: 'meal not found',
        },
        HttpStatus.BAD_REQUEST
      );

    let mealDoc = meal.toObject();
    let foods = [];
    if (mealDoc.mealFoods.length) {
      for (const food of mealDoc.mealFoods) {
        const foodData = await this.foodService.findOne(food.foodId.toString());
        let foodDoc = foodData.toObject();

        foods.push({ ...foodDoc, serving: food.serving });
      }
    }
    // find review
    const review = await this.reviewService.findMealReview({ meal: id });

    return { ...mealDoc, mealFoods: foods, review };
  }

  async update(id: string, updateMealDto: UpdateMealDto) {
    const updatedMeal = await this.mealModel.findByIdAndUpdate(
      id,
      { ...updateMealDto, updatedAt: new Date().valueOf() },
      { new: true }
    );

    let mealDoc = updatedMeal.toObject();

    let foods = [];
    if (mealDoc.mealFoods.length) {
      for (const food of mealDoc.mealFoods) {
        const foodData = await this.foodService.findOne(food.foodId.toString());
        let foodDoc = foodData.toObject();

        foods.push({ ...foodDoc, serving: food.serving });
      }
    }

    return {
      ...mealDoc,
      mealFoods: foods,
    };
  }

  remove(id: number) {
    return `This action removes a #${id} meal`;
  }

  async getAllClientMealsByCoach(coachId: string, query: any) {
    const { page = 1, limit = 10 } = query;
    const totalCount = await this.mealModel.countDocuments({ coach: coachId });
    let totalPages = Math.ceil(totalCount / limit);
    const meals: any = await this.mealModel
      .find({ coach: coachId })
      .skip((page - 1) * parseInt(limit))
      .sort('-updatedAt')
      .limit(parseInt(limit))
      .populate({
        path: 'client',
        select: '_id name image', // replace with the actual fields you want to include
      });
    const mealsData = [];
    for (let meal of meals) {
      const mealIds = meal.mealFoods.map((ele) => ele.foodId);
      const where = { _id: { $in: mealIds } };
      const foods = await this.foodService.findByWhere(where);

      // Assign foods directly to mealFoods and calculate totals
      let totalCalories = 0;
      let totalProteins = 0;
      let totalCarbs = 0;
      let totalFats = 0;

      meal.mealFoods = meal.mealFoods.map((mealFood) => {
        mealFood.food = foods.find((food) => food._id.equals(mealFood.foodId));
        if (mealFood.food) {
          const servingMultiplier = parseFloat(mealFood.serving);
          totalCalories += mealFood.food.calories * servingMultiplier;
          totalProteins += mealFood.food.proteins * servingMultiplier;
          totalCarbs += mealFood.food.carbs * servingMultiplier;
          totalFats += mealFood.food.fats * servingMultiplier;
        }

        return mealFood;
      });
      // Add total nutrients to the meal object
      meal = meal.toObject();
      const review = await this.reviewService.findOneReview(
        coachId,
        meal.client,
        meal._id
      );
      meal.review = review;
      meal.totalNutrients = {
        totalCalories,
        totalProteins,
        totalCarbs,
        totalFats,
      };
      if (meal.client.image) {
        const url = await this.uploadImageService.getObjectSignedUrl(
          meal.client.image
        );
        meal.client.image = url;
      }

      mealsData.push(meal);
    }

    // console.log(mealsData, { depth: null });
    return {
      mealsData,
      pagination: {
        page,
        totalPages,
        pageSize: limit,
        hasNextPage: totalPages === parseInt(page) ? false : true,
      },
    };
  }
}
