import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Workout } from './schema/workout.schema';
import { Model, Types } from 'mongoose';
import { AddWorkoutDTO } from './dto/addWorkout.dto';
import { UpdateWorkoutDTO } from './dto/updateWorkout.dto';
import { FilterWorkoutDTO } from './dto/filterWorkout.dto';

@Injectable()
export class WorkoutService {
  constructor(@InjectModel(Workout.name) private workoutModel: Model<Workout>,
  ) { }

  // * CREATE WORKOUT
  async create(body: AddWorkoutDTO) {
    let workout = (await (await this.workoutModel.create(body)).populate("exercises")).populate("client");
    return workout;
  }

  // * FIND ALL WORKOUT
  async findAll(filter: FilterWorkoutDTO) {
    let query: any = {};

    // COMPARE CLIENT
    if (filter.client) {
      query["client"] = new Types.ObjectId(filter.client);
    }

    // COMPARE DATE
    if (filter.date) {
      const start = new Date(filter.date);
      start.setHours(0, 0, 0, 0);
      const end = new Date(filter.date);
      end.setHours(23, 59, 59, 999);
      query["createdAt"] = { $gte: start, $lte: end };
    }

    let response = await this.workoutModel.find(query).select("-__v").populate("exercises").populate("client");
    return response;
  }

  // * FIND WORKOUT BY ID
  async findById(id: string) {
    let response = await this.workoutModel.findById(id).select("-__v").populate("exercises").populate("client");
    return response;
  }

  // * UPDATE WORKOUT BY ID
  async updateById(id: string, payload: UpdateWorkoutDTO) {
    let response = await this.workoutModel.findById(id, payload, { new: true }).select("-__v").populate("exercises").populate("client");
    return response;
  }

  // * DELETE WORKOUT BY ID
  async deleteById(id: string) {
    return await this.workoutModel.findByIdAndDelete(id);
  }
}
