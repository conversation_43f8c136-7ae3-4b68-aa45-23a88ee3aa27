import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, SchemaFactory } from "@nestjs/mongoose";
import * as mongoose from "mongoose";
import { I<PERSON>orkout } from "../interface/workout.interface";
import { Exercises } from "../../execises/schema/exercises.schema";
import { Client } from "../../client/schemas/client.schema";

@Schema({ timestamps: true })
export class Workout extends mongoose.Document implements IWorkout {
  @Prop({
    type: [{ type: mongoose.Schema.Types.ObjectId, ref: Exercises.name }],
    required: true
  })
  exercises: mongoose.Schema.Types.ObjectId[];

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: Client.name })
  client: mongoose.Schema.Types.ObjectId;
}

export const WorkoutSchema = SchemaFactory.createForClass(Workout);