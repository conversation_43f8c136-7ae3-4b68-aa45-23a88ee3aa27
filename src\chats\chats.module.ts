import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { ChatsService } from "./chats.service";
import { <PERSON><PERSON><PERSON>ontroller } from "./chats.controller";
import { MongooseModule } from "@nestjs/mongoose";
import { <PERSON><PERSON>, ChatSchema } from "./schemas/chat.schema";
import { MessagesModule } from "../messages/messages.module";
import { WebSocketModule } from "../chat/web-socket.module";
import { Coach, CoachSchema } from "src/coach/schemas/coach.schema";
import { Client, ClientSchema } from "src/client/schemas/client.schema";
import { UploadImageModule } from "src/upload-image/upload-image.module";
import { Message, MessageSchema } from "src/messages/schemas/message.schema";
import { User, UserSchema } from "src/users/schemas/user.schema";
import { Notifications, NotificationsSchema } from "src/notifications/schema/notification.schema";
import { NotificationsModule } from "src/notifications/notifications.module";
import { UsersModule } from "src/users/users.module";
import { NotificationsService } from "src/notifications/notifications.service";

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Chat.name, schema: ChatSchema },
      { name: Coach.name, schema: CoachSchema },
      { name: Client.name, schema: ClientSchema },
      { name: Message.name, schema: MessageSchema },
      { name: User.name, schema: UserSchema },
      { name: Notifications.name, schema: NotificationsSchema },
    ]),
    MessagesModule,
    WebSocketModule,
    UploadImageModule,
    UsersModule,
    NotificationsModule,
  ],
  controllers: [ChatsController],
  providers: [ChatsService],
})
export class ChatsModule {}
