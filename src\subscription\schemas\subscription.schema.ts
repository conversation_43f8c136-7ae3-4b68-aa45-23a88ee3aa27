import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from "@nestjs/mongoose";

@Schema({ timestamps: true })
export class Subscription {
  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  services: string[];

  @Prop({ required: true })
  validity: number;

  @Prop({ required: true })
  price: string;

  @Prop({ required: true })
  plan: string;
}

export const SubscriptionSchema = SchemaFactory.createForClass(Subscription);
