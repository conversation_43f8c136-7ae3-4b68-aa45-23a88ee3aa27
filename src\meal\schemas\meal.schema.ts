import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import mongoose from "mongoose";
import { Client } from "src/client/schemas/client.schema";
import { Coach } from "src/coach/schemas/coach.schema";

@Schema({ timestamps: false })
export class Meal {
  @Prop({ required: true })
  mealTitle: string;
  @Prop({ required: true })
  mealFoods: [
    {
      foodId: string;
      serving: number;
    }
  ];
  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: "Client",
    required: true,
  })
  client: mongoose.Types.ObjectId | Client;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: "Coach",
    required: false,
  })
  coach: mongoose.Types.ObjectId | Coach;

  @Prop({ required: false })
  createdAt: string;

  @Prop({ required: false })
  updatedAt: string;
}

export const MealSchema = SchemaFactory.createForClass(Meal);
