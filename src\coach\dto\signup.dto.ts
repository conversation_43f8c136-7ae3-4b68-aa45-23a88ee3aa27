// export class CreateUserDto {}
import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsEmail, IsOptional, IsN<PERSON><PERSON> } from "class-validator";

export class SignUpDto {
  @ApiProperty({ example: "jon wick" })
  @IsString()
  name: string;

  @ApiProperty({ example: "coach" })
  @IsString()
  userType: string;

  @ApiProperty({ example: "<EMAIL>" })
  @IsEmail()
  email: string;

  @ApiProperty({ example: "causewaysecret" })
  @IsString()
  password: string;

  @ApiProperty({ example: "birthday" })
  @IsString()
  @IsOptional()
  dob: string;

  @ApiProperty({ example: "some experience" })
  @IsString()
  @IsOptional()
  experience: string;

  @ApiProperty({ example: "I am coach" })
  @IsString()
  @IsOptional()
  about: string;

  @ApiProperty({ example: "hoto url" })
  @IsString()
  @IsOptional()
  photo?: string;
}
