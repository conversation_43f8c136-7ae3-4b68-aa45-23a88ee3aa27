import { <PERSON><PERSON><PERSON>, <PERSON>p, SchemaFactory } from '@nestjs/mongoose';
import * as mongoose from 'mongoose';
import {
  IChat
} from '../interfaces/chats.interface';

@Schema({ timestamps: true })
export class Chat extends mongoose.Document implements IChat {


  @Prop({ type: [mongoose.Schema.Types.ObjectId], ref: 'User', required: true })
  users: string[];

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Message', required: false })
  lastMessage: string;

}

export const ChatSchema = SchemaFactory.createForClass(Chat);




