import { Modu<PERSON> } from "@nestjs/common";
import { DocumentService } from "./document.service";
import { DocumentController } from "./document.controller";
import { MongooseModule } from "@nestjs/mongoose";
import { DocumentSchema, Document } from "./schemas/document.schema";
import { UploadImageModule } from "src/upload-image/upload-image.module";

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Document.name, schema: DocumentSchema },
    ]),
    UploadImageModule,
  ],
  controllers: [DocumentController],
  providers: [DocumentService],
})
export class DocumentModule {}
