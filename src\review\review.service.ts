import { Injectable } from "@nestjs/common";
import { CreateReviewDto } from "./dto/create-review.dto";
import { UpdateReviewDto } from "./dto/update-review.dto";
import { InjectModel } from "@nestjs/mongoose";
import { Review } from "./schemas/review.schema";
import { Model, } from "mongoose";
import { NotificationsService } from "src/notifications/notifications.service";
import { UsersService } from "src/users/users.service";

@Injectable()
export class ReviewService {
  constructor(@InjectModel(Review.name) private reviewModel: Model<Review>,
    private usersServices: UsersService,
    private notificationsService: NotificationsService
  ) { }
  async create(createReviewDto: CreateReviewDto) {
    let review = await this.reviewModel.create({
      ...createReviewDto,
      createdAt: new Date().valueOf(),
      updatedAt: new Date().valueOf(),
    });

    let client = await this.usersServices.findOne({ _id: createReviewDto.client });

    if (client && client.fcmToken) {
      this.notificationsService.create({ title: "New Review", body: "Your meal is reviewed by coach.", fcmToken: client.fcmToken, user: createReviewDto.client });
    }
    return review;
  }

  findOneReview(coachId: string, clientId: string, mealId: string) {
    return this.reviewModel.findOne({
      coach: coachId,
      client: clientId,
      meal: mealId,
    });
  }

  findOne(mealId: string, foodId: string) {
    return this.reviewModel.findOne({ meal: mealId, food: foodId });
  }

  update(id: string, updateReviewDto: UpdateReviewDto) {
    return this.reviewModel.updateOne({ _id: id }, updateReviewDto);
  }

  remove(id: string) {
    return this.reviewModel.findOneAndDelete({ _id: id });
  }

  findMealReview(where: object) {
    return this.reviewModel.findOne(where);
  }
}
