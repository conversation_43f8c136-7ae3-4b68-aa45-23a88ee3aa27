import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { InvitesService } from "./invites.service";
import { InvitesController } from "./invites.controller";
import { MongooseModule } from "@nestjs/mongoose";
import { Invite, InviteSchema } from "./schemas/invite.schema";
import { CoachModule } from "src/coach/coach.module";

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Invite.name, schema: InviteSchema }]),
    CoachModule,
  ],
  controllers: [InvitesController],
  providers: [InvitesService],
})
export class InvitesModule {}
