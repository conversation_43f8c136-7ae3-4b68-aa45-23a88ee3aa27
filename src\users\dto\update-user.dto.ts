import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateUserDto } from './create-user.dto';
import {
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
  IsBoolean
} from 'class-validator';
import { Type } from 'class-transformer';

class License {
  @ApiProperty({ example: 'jon wick' })
  @IsString()
  name: string;

  @ApiProperty({ example: 'USA' })
  @IsString()
  country: string;

  @ApiProperty({ example: 'USA-123' })
  @IsString()
  licenseNumber: string;

  @ApiProperty({ example: 'url' })
  @IsString()
  driverLicenseFrontPhoto: string;

  @ApiProperty({ example: 'url' })
  @IsString()
  driverLicenseBackPhoto: string;

  @ApiProperty({ example: 'url' })
  @IsString()
  passportOrICFrontPhoto: string;

  @ApiProperty({ example: 'url' })
  @IsString()
  passportOrICBackPhoto: string;
}

export class UpdateUserDto {
  @ApiProperty({ example: 'jon wick' })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ example: '*********' })
  @IsString()
  @IsOptional()
  phoneNumber?: string;

  @ApiProperty({ example: 'url' })
  @IsString()
  @IsOptional()
  photo?: string;

  @ApiProperty({ example: true })
  @IsBoolean()
  @IsOptional()
  active?: boolean;

  @ApiProperty()
  @ValidateNested({ each: true })
  @IsObject()
  @IsOptional()
  @Type(() => License)
  license?: License;
}
